"use server"

import Groq from "groq-sdk";

export interface ChatCompletionMessageParam {
  role: "system" | "user" | "assistant";
  content: string;
}


function getApiKey() {
  return (
    process.env.GROQ_API_KEY|| ""
  );
}

export interface LlmResponse {
  message: string;
  type: "general" | "question" | "end";
  category?: "communication" | "technical" | "behavioral" | null;
}

export async function llamaChat(
  messages: ChatCompletionMessageParam[],
  model = "meta-llama/llama-4-maverick-17b-128e-instruct"
): Promise<LlmResponse> {
  const apiKey = getApiKey();
  if (!apiKey) throw new Error("Missing GROQ_API_KEY env var");

  const groq = new Groq({ apiKey });
  const data = await groq.chat.completions.create({ model, messages });
  const content: string = (data.choices?.[0] as any)?.message?.content ?? "";
  try {
    return JSON.parse(content) as LlmResponse;
  } catch {
    return { message: content, type: "general" };
  }
}

// Endpoint for whisper transcription (OpenAI-compatible path on Groq Cloud)
const AUDIO_ENDPOINT = "https://api.groq.com/openai/v1/audio/transcriptions";

// SDK currently (Jun 2025) does not expose audio endpoints, so leave fetch fallback.
export async function whisperTranscribe(audio: Blob): Promise<string> {
  const apiKey = getApiKey();
  if (!apiKey) throw new Error("Missing GROQ API key.");

  const form = new FormData();
  form.append("file", audio, "audio.webm");
  form.append("model", "whisper-large-v3-turbo");

  const res = await fetch(AUDIO_ENDPOINT, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
    },
    body: form,
  });

  if (!res.ok) {
    throw new Error(`Groq whisper error: ${await res.text()}`);
  }

  const data = await res.json();
  return data.text as string;
}
