export const interviewQuestions = {
  communication: [
    "How do you explain financial data?",
    "How do you handle client conflict?",
    "Describe your communication style.",
    "How do you present reports clearly?",
  ],
  technical: [
    "What is GAAP vs IFRS?",
    "How do audits add value?",
    "Explain a cash flow statement.",
    "What is deferred tax liability?",
  ],
  behavioral: [
    "How do you manage deadlines?",
    "Describe a successful project.",
    "How do you handle pressure?",
    "What’s your biggest strength?",
  ],
};

export const CATEGORIES = Object.keys(interviewQuestions) as (keyof typeof interviewQuestions)[];
export const QUESTIONS_PER_CATEGORY = 1;
