"use client";

import { useState } from "react";
import { FileText, Upload, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { analyzeCoverLetter, type CoverLetterAnalysisResult } from "./actions";
import { CoverLetterAnalysis } from "@/components/dashboard/cover-letter/cover-letter-analysis";

export default function CoverLetterPage() {
  const [content, setContent] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<CoverLetterAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleAnalyze = async () => {
    if (!content.trim()) {
      setError("Please enter your cover letter content");
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const result = await analyzeCoverLetter(content);
      
      if (result.success && result.analysis) {
        setAnalysisResult(result.analysis);
      } else {
        setError(result.error || "Failed to analyze cover letter");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleReset = () => {
    setContent("");
    setAnalysisResult(null);
    setError(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== "text/plain") {
      setError("Please upload a text file (.txt)");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      setContent(text);
      setError(null);
    };
    reader.readAsText(file);
  };

  if (analysisResult) {
    return (
      <CoverLetterAnalysis
        result={analysisResult}
        onReset={handleReset}
      />
    );
  }

  return (
    <div className="p-8 md:p-12">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Cover Letter Analysis
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Get professional feedback on your cover letter with AI-powered analysis
            focused on structure, content quality, and persuasive impact.
          </p>
        </div>

        {/* Input Section */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cover Letter Content
            </label>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Paste your cover letter content here or upload a text file..."
              className="min-h-[300px] resize-none"
              maxLength={2000}
            />
            <div className="flex justify-between items-center mt-2">
              <p className="text-sm text-gray-500">
                {content.length}/2000 characters
              </p>
              <p className="text-sm text-gray-500">
                ~{content.trim().split(/\s+/).filter(word => word.length > 0).length} words
              </p>
            </div>
          </div>

          {/* File Upload */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Or upload a text file
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="flex items-center px-4 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload .txt file
              </label>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Analyze Button */}
          <Button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !content.trim()}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Analyzing Cover Letter...
              </>
            ) : (
              <>
                <FileText className="w-4 h-4 mr-2" />
                Analyze Cover Letter
              </>
            )}
          </Button>
        </div>

        {/* Tips Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            Cover Letter Tips
          </h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">Structure</h4>
              <ul className="space-y-1">
                <li>• Clear opening with position reference</li>
                <li>• 2-3 body paragraphs with examples</li>
                <li>• Strong closing with call to action</li>
                <li>• 250-400 words total</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Content</h4>
              <ul className="space-y-1">
                <li>• Specific achievements with metrics</li>
                <li>• Company research demonstration</li>
                <li>• Role-specific requirements addressed</li>
                <li>• Professional yet engaging tone</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
