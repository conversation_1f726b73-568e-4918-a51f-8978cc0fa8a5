"use client";

import { useState } from "react";
import { CoverLetterUploader } from "@/components/dashboard/cover-letter/cover-letter-uploader";
import { CoverLetterLoading } from "@/components/dashboard/cover-letter/cover-letter-loading";
import { analyzeCoverLetter, type CoverLetterAnalysisResult } from "./actions";
import { CoverLetterAnalysis } from "@/components/dashboard/cover-letter/cover-letter-analysis";

export default function CoverLetterPage() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] =
    useState<CoverLetterAnalysisResult | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleFileUpload = async (file: File) => {
    setUploadedFile(file);
    setIsAnalyzing(true);
    setAnalysisResult(null);

    const formData = new FormData();
    formData.append("file", file);

    try {
      // Extract text from file
      const extractResponse = await fetch("/api/extract-text", {
        method: "POST",
        body: formData,
      });

      if (!extractResponse.ok) {
        throw new Error("Failed to extract text from file");
      }

      const { text } = await extractResponse.json();

      // Analyze the extracted text
      const result = await analyzeCoverLetter(text);

      if (result.success && result.analysis) {
        setAnalysisResult(result.analysis);
      } else {
        throw new Error(result.error || "Failed to analyze cover letter");
      }
    } catch (error) {
      console.error("Error analyzing cover letter:", error);
      // Reset state on error
      setUploadedFile(null);
      setAnalysisResult(null);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleReset = () => {
    setUploadedFile(null);
    setAnalysisResult(null);
    setIsAnalyzing(false);
  };

  if (analysisResult) {
    return (
      <CoverLetterAnalysis result={analysisResult} onReset={handleReset} />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Cover Letter Analysis
          </h1>
          <p className="text-gray-600">
            Get professional feedback on your cover letter with AI-powered
            analysis
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {!uploadedFile && !isAnalyzing && !analysisResult && (
            <CoverLetterUploader onFileUpload={handleFileUpload} />
          )}

          {isAnalyzing && <CoverLetterLoading fileName={uploadedFile?.name} />}

          {analysisResult && (
            <CoverLetterAnalysis
              result={analysisResult}
              onReset={handleReset}
            />
          )}
        </div>
      </div>
    </div>
  );
}
