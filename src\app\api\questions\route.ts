import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { questions, firms } from "@/db/schema";
import { eq, and, or } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const firmName = searchParams.get("firm");

    if (!category || !firmName) {
      return NextResponse.json(
        { error: "Category and firm parameters are required" },
        { status: 400 }
      );
    }

    // Get the firm ID for the selected firm
    const selectedFirm = await db
      .select()
      .from(firms)
      .where(eq(firms.name, firmName.toUpperCase()))
      .limit(1);

    if (selectedFirm.length === 0) {
      return NextResponse.json(
        { error: "Firm not found" },
        { status: 404 }
      );
    }

    // Get EY firm ID (always included)
    const eyFirm = await db
      .select()
      .from(firms)
      .where(eq(firms.name, "EY"))
      .limit(1);

    if (eyFirm.length === 0) {
      return NextResponse.json(
        { error: "EY firm not found in database" },
        { status: 404 }
      );
    }

    // Fetch questions from both the selected firm and EY
    const firmIds = [selectedFirm[0].id];
    if (selectedFirm[0].id !== eyFirm[0].id) {
      firmIds.push(eyFirm[0].id);
    }

    const questionList = await db
      .select({
        id: questions.id,
        questionText: questions.questionText,
        category: questions.category,
        firmId: questions.firmId,
        difficulty: questions.difficulty,
      })
      .from(questions)
      .where(
        and(
          eq(questions.category, category),
          or(...firmIds.map(id => eq(questions.firmId, id)))
        )
      );

    // Shuffle the questions to randomize order
    const shuffledQuestions = questionList.sort(() => Math.random() - 0.5);

    return NextResponse.json({
      questions: shuffledQuestions,
      count: shuffledQuestions.length,
      category,
      firm: firmName,
    });

  } catch (error) {
    console.error("Error fetching questions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
