"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>cle,
  FileText,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Download,
  Star,
  MessageSquare,
  Target,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export interface CoverLetterAnalysisResult {
  rating: number;
  generalFeedback: string;
  improvements: Array<{
    category: string;
    priority: "High" | "Medium" | "Low";
    suggestions: string[];
  }>;
  strengths: string[];
  weaknesses: string[];
  structure: {
    hasOpening: boolean;
    hasBody: boolean;
    hasClosing: boolean;
    hasCallToAction: boolean;
  };
  tone: "Professional" | "Casual" | "Mixed" | "Unclear";
  wordCount: number;
}

interface CoverLetterAnalysisProps {
  result: CoverLetterAnalysisResult;
  onReset: () => void;
}

export function CoverLetterAnalysis({ result, onReset }: CoverLetterAnalysisProps) {
  const [activeTab, setActiveTab] = useState<"overview" | "improvements" | "structure">("overview");

  const getRatingColor = (rating: number) => {
    if (rating >= 80) return "bg-green-100 text-green-800";
    if (rating >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const getRatingIcon = (rating: number) => {
    if (rating >= 80) return <CheckCircle className="w-6 h-6" />;
    if (rating >= 60) return <Star className="w-6 h-6" />;
    return <AlertTriangle className="w-6 h-6" />;
  };

  const getRatingText = (rating: number) => {
    if (rating >= 90) return "Excellent";
    if (rating >= 80) return "Very Good";
    if (rating >= 70) return "Good";
    if (rating >= 60) return "Fair";
    return "Needs Improvement";
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800 border-red-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Low":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getToneColor = (tone: string) => {
    switch (tone) {
      case "Professional":
        return "text-green-600";
      case "Casual":
        return "text-blue-600";
      case "Mixed":
        return "text-yellow-600";
      default:
        return "text-gray-600";
    }
  };

  const downloadReport = () => {
    const reportContent = generateReportText(result);
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cover-letter-analysis-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generateReportText = (analysis: CoverLetterAnalysisResult) => {
    const date = new Date().toLocaleDateString();
    return `
PROFESSIONAL COVER LETTER ANALYSIS REPORT
Generated on: ${date}

===========================================
OVERALL ASSESSMENT
===========================================
Overall Score: ${analysis.rating}/100 (${getRatingText(analysis.rating)})
Word Count: ${analysis.wordCount} words
Tone: ${analysis.tone}

===========================================
STRUCTURE ANALYSIS
===========================================
Opening Paragraph: ${analysis.structure.hasOpening ? "✓ Present" : "✗ Missing"}
Body Content: ${analysis.structure.hasBody ? "✓ Present" : "✗ Missing"}
Closing Paragraph: ${analysis.structure.hasClosing ? "✓ Present" : "✗ Missing"}
Call to Action: ${analysis.structure.hasCallToAction ? "✓ Present" : "✗ Missing"}

===========================================
GENERAL FEEDBACK
===========================================
${analysis.generalFeedback}

===========================================
KEY STRENGTHS
===========================================
${analysis.strengths.map((strength, index) => `${index + 1}. ${strength}`).join('\n')}

===========================================
AREAS FOR IMPROVEMENT
===========================================
${analysis.weaknesses.map((weakness, index) => `${index + 1}. ${weakness}`).join('\n')}

===========================================
IMPROVEMENT RECOMMENDATIONS
===========================================
${analysis.improvements.map((improvement, index) => `
${index + 1}. ${improvement.category} (${improvement.priority} Priority)
   Suggestions:
   ${improvement.suggestions.map(suggestion => `   • ${suggestion}`).join('\n')}
`).join('\n')}

===========================================
NEXT STEPS
===========================================
1. Focus on High priority improvements first
2. Ensure all structural elements are present
3. Tailor content to specific job applications
4. Proofread for grammar and spelling errors
5. Test different versions for effectiveness

This analysis was generated by Preparify's AI-powered cover letter review system.
    `.trim();
  };

  const categoryIcons = {
    "Structure & Format": FileText,
    "Content Quality": TrendingUp,
    "Tone & Style": MessageSquare,
    "Personalization": Target,
    "Impact & Persuasion": Star,
  };

  const getCategoryIcon = (category: string) => {
    const Icon = categoryIcons[category as keyof typeof categoryIcons] || FileText;
    return <Icon className="w-5 h-5" />;
  };

  return (
    <div className="p-8 md:p-12">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Cover Letter Analysis Results
          </h2>
        </div>

        {/* Rating Card */}
        <div className="mb-8 p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Overall Score</h3>
              <div className="flex items-center space-x-3">
                <div className={`p-3 rounded-full ${getRatingColor(result.rating)}`}>
                  {getRatingIcon(result.rating)}
                </div>
                <div>
                  <p className="text-3xl font-bold text-gray-900">
                    {result.rating}
                    <span className="text-lg text-gray-500 ml-1">/ 100</span>
                  </p>
                  <p className={`text-sm font-medium ${getRatingColor(result.rating).split(' ')[0]}`}>
                    {getRatingText(result.rating)}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Word Count</h3>
              <p className="text-2xl font-bold text-gray-900">{result.wordCount}</p>
              <p className="text-sm text-gray-600">
                {result.wordCount >= 250 && result.wordCount <= 400 ? "Optimal length" : 
                 result.wordCount < 250 ? "Too short" : "Too long"}
              </p>
            </div>
            
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Tone</h3>
              <p className={`text-lg font-semibold ${getToneColor(result.tone)}`}>{result.tone}</p>
              <p className="text-sm text-gray-600">Communication Style</p>
            </div>
            
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Structure</h3>
              <div className="flex space-x-1">
                {Object.values(result.structure).map((present, index) => (
                  <div
                    key={index}
                    className={`w-3 h-3 rounded-full ${present ? 'bg-green-500' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
              <p className="text-sm text-gray-600">
                {Object.values(result.structure).filter(Boolean).length}/4 elements
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Overview', icon: FileText },
                { id: 'improvements', label: 'Improvements', icon: TrendingUp },
                { id: 'structure', label: 'Structure', icon: CheckCircle },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* General Feedback */}
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">General Assessment</h3>
                <p className="text-gray-700 leading-relaxed">{result.generalFeedback}</p>
              </div>

              {/* Strengths and Weaknesses */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-green-900 mb-4 flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Strengths
                  </h3>
                  <ul className="space-y-2">
                    {result.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-green-800">{strength}</p>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="bg-orange-50 border border-orange-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-orange-900 mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2" />
                    Areas for Improvement
                  </h3>
                  <ul className="space-y-2">
                    {result.weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-orange-800">{weakness}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Improvements Tab */}
          {activeTab === "improvements" && (
            <div className="space-y-4">
              {result.improvements.map((improvement, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-900 flex items-center">
                      {getCategoryIcon(improvement.category)}
                      <span className="ml-2">{improvement.category}</span>
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(improvement.priority)}`}>
                      {improvement.priority} Priority
                    </span>
                  </div>

                  <ul className="space-y-3">
                    {improvement.suggestions.map((suggestion, suggestionIndex) => (
                      <li key={suggestionIndex} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-gray-700 leading-relaxed">{suggestion}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          {/* Structure Tab */}
          {activeTab === "structure" && (
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-6">Structure Analysis</h3>
              <div className="grid gap-4">
                {[
                  { key: 'hasOpening', label: 'Opening Paragraph', description: 'Clear introduction with position reference' },
                  { key: 'hasBody', label: 'Body Content', description: 'Substantial content with examples and achievements' },
                  { key: 'hasClosing', label: 'Closing Paragraph', description: 'Professional conclusion' },
                  { key: 'hasCallToAction', label: 'Call to Action', description: 'Clear next steps or interview request' },
                ].map((item) => {
                  const isPresent = result.structure[item.key as keyof typeof result.structure];
                  return (
                    <div key={item.key} className={`p-4 rounded-lg border ${isPresent ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                      <div className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${isPresent ? 'bg-green-500' : 'bg-red-500'}`}>
                          {isPresent ? (
                            <CheckCircle className="w-4 h-4 text-white" />
                          ) : (
                            <AlertTriangle className="w-4 h-4 text-white" />
                          )}
                        </div>
                        <div>
                          <h4 className={`font-medium ${isPresent ? 'text-green-900' : 'text-red-900'}`}>
                            {item.label}
                          </h4>
                          <p className={`text-sm ${isPresent ? 'text-green-700' : 'text-red-700'}`}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row gap-3">
          <Button
            onClick={downloadReport}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
          >
            <Download className="w-4 h-4 mr-2" />
            Download Report
          </Button>
          
          <Button
            onClick={onReset}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Analyze Another
          </Button>
        </div>
      </div>
    </div>
  );
}
