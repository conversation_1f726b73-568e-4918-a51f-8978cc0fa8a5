export const getInterviewQuestion = (category: 'technical' | 'behavioral' | 'case-based'): string => {
  const questions = {
    technical: [
      'What are the main differences between REST and GraphQL?',
      'Explain the concept of dependency injection.',
      'Describe the SOLID principles of object-oriented design.',
    ],
    behavioral: [
      'Tell me about a time you had a conflict with a coworker.',
      'Describe a situation where you had to learn a new technology quickly.',
      'How do you handle tight deadlines and pressure?',
    ],
    'case-based': [
      'Our client is a major airline experiencing a decline in customer satisfaction. What steps would you take to identify the root cause and propose a solution?',
      'A new tech startup wants to enter the ride-sharing market. What is their go-to-market strategy?',
      'How would you estimate the number of coffee shops in New York City?',
    ],
  };

  const categoryQuestions = questions[category];
  return categoryQuestions[Math.floor(Math.random() * categoryQuestions.length)];
};
