"use server";

import { z } from "zod";
import {
  detectIndustry,
  getIndustrySpecificPrompt,
  industryStandards,
} from "@/lib/industryAnalysis";
import {
  generateSpecificFeedback,
  getIndustryFeedbackTemplates,
  professionalFeedbackPhrases,
} from "@/lib/feedbackTemplates";

// Enhanced schema for comprehensive resume analysis
const ResumeAnalysisSchema = z.object({
  rating: z.number().int().min(0).max(100),
  generalFeedback: z.string(),
  industrySpecificFeedback: z.string(),
  atsImprovements: z.array(
    z.object({
      category: z.string(),
      priority: z.enum(["Critical", "High", "Medium", "Low"]),
      suggestions: z.array(z.string()),
      impact: z.string(),
    })
  ),
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  detailedScoring: z.object({
    contentQuality: z.number().int().min(0).max(100),
    formatting: z.number().int().min(0).max(100),
    atsCompatibility: z.number().int().min(0).max(100),
    industryAlignment: z.number().int().min(0).max(100),
    achievementQuantification: z.number().int().min(0).max(100),
    keywordOptimization: z.number().int().min(0).max(100),
  }),
  careerLevel: z.enum(["Entry", "Mid", "Senior", "Executive"]),
  industryFocus: z.string(),
  recommendedActions: z.array(
    z.object({
      action: z.string(),
      priority: z.enum(["Critical", "High", "Medium", "Low"]),
      timeToImplement: z.string(),
      expectedImpact: z.string(),
    })
  ),
});

export type AnalysisResult = z.infer<typeof ResumeAnalysisSchema>;

// Extract text from PDF using pdf-parse
async function extractTextFromPDF(pdfBuffer: Buffer): Promise<string> {
  try {
    const pdfParse = (await import("pdf-parse")).default;

    console.log("PDF Buffer size:", pdfBuffer.length);

    if (!Buffer.isBuffer(pdfBuffer) || pdfBuffer.length === 0) {
      throw new Error("Invalid PDF buffer provided");
    }

    const pdfHeader = pdfBuffer.slice(0, 4).toString();
    if (pdfHeader !== "%PDF") {
      console.error("Buffer doesn't start with PDF header:", pdfHeader);
      throw new Error("Invalid PDF format - missing PDF header");
    }

    const data = await pdfParse(pdfBuffer);

    if (!data.text || data.text.trim().length === 0) {
      throw new Error(
        "No text found in PDF - document may be image-based or corrupted"
      );
    }

    console.log("Successfully extracted text, length:", data.text.length);
    return data.text;
  } catch (error) {
    console.error("Error extracting text from PDF:", error);

    if (error instanceof Error) {
      if (error.message.includes("ENOENT")) {
        throw new Error(
          "PDF processing error: File access issue. Please try uploading the file again."
        );
      }
      if (error.message.includes("Invalid PDF format")) {
        throw new Error(
          "Invalid PDF format. Please ensure the file is a valid PDF document."
        );
      }
      if (error.message.includes("No text found")) {
        throw new Error(
          "Cannot extract text from PDF. The document may be image-based or password-protected."
        );
      }
    }

    throw new Error(
      "Failed to extract text from PDF. Please try a different PDF file."
    );
  }
}

export async function analyzeResume(
  formData: FormData
): Promise<{ success: true; analysis: AnalysisResult } | { error: string }> {
  const file = formData.get("resume") as File;

  if (!file) {
    return { error: "No file uploaded." };
  }

  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/webp",
  ];
  if (!allowedTypes.includes(file.type)) {
    return {
      error: "Invalid file type. Please upload a PDF, JPG, PNG, or WebP file.",
    };
  }

  const maxSize =
    file.type === "application/pdf" ? 10 * 1024 * 1024 : 4 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      error: `File size too large. Please upload a file smaller than ${
        file.type === "application/pdf" ? "10MB" : "4MB"
      }.`,
    };
  }

  let resumeContent = "";
  let contentType = "";

  try {
    console.log(
      "Processing file:",
      file.name,
      "Type:",
      file.type,
      "Size:",
      file.size
    );

    const arrayBuffer = await file.arrayBuffer();
    console.log("ArrayBuffer size:", arrayBuffer.byteLength);

    if (file.type === "application/pdf") {
      const pdfBuffer = Buffer.from(arrayBuffer);
      console.log("Created PDF Buffer, size:", pdfBuffer.length);

      resumeContent = await extractTextFromPDF(pdfBuffer);
      contentType = "text";

      console.log("Extracted PDF text length:", resumeContent.length);
    } else {
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      resumeContent = `data:${file.type};base64,${base64}`;
      contentType = "image";
    }
  } catch (error) {
    console.error("Error processing file:", error);
    if (error instanceof Error) {
      if (error.message.includes("PDF")) {
        return { error: error.message };
      }
    }
    return { error: "Failed to process file content." };
  }

  const groqApiKey = process.env.GROQ_API_KEY;
  if (!groqApiKey) {
    console.error("GROQ_API_KEY is not set in environment variables.");
    return { error: "Server configuration error: AI API key not found." };
  }

  // Try different models in order of preference
  const models = [
    "llama-3.1-8b-instant",
    "llama-3.1-70b-versatile",
    "mixtral-8x7b-32768",
  ];

  // Detect industry for specialized analysis
  const detectedIndustry = detectIndustry(resumeContent);
  const industryPrompt = getIndustrySpecificPrompt(detectedIndustry);
  const industryName = industryStandards[detectedIndustry]?.name || "General";

  for (const model of models) {
    try {
      console.log(
        `Attempting analysis with model: ${model} for industry: ${industryName}`
      );

      const messages = [
        {
          role: "system",
          content: `You are a senior HR professional and resume expert with 15+ years of experience in Big 4 consulting, investment banking, and Fortune 500 recruiting. You specialize in evaluating resumes for professional services, finance, consulting, and corporate roles.

ANALYSIS FRAMEWORK:
You must evaluate resumes using professional industry standards, focusing on:

1. CONTENT QUALITY (25% of score):
   - Achievement quantification with specific metrics (revenue, percentages, team sizes)
   - Action verb usage and impact statements
   - Progression and career growth demonstration
   - Relevance to target industry/role

2. PROFESSIONAL PRESENTATION (20% of score):
   - Clean, professional formatting
   - Consistent styling and spacing
   - Appropriate length (1-2 pages for most roles)
   - Clear section organization

3. ATS COMPATIBILITY (15% of score):
   - Standard section headers
   - Keyword optimization for target roles
   - Readable fonts and formatting
   - No graphics/tables that break ATS parsing

4. INDUSTRY ALIGNMENT (20% of score):
   - Industry-specific terminology and skills
   - Relevant certifications and education
   - Understanding of sector requirements
   - Professional standards adherence

5. ACHIEVEMENT QUANTIFICATION (10% of score):
   - Specific numbers, percentages, dollar amounts
   - Before/after comparisons
   - Scale and scope indicators
   - Measurable outcomes

6. KEYWORD OPTIMIZATION (10% of score):
   - Role-relevant keywords naturally integrated
   - Skills section completeness
   - Industry buzzwords appropriately used
   - Technical competencies highlighted

INDUSTRY-SPECIFIC STANDARDS:
For Big 4/Professional Services roles, prioritize:
- Client management and relationship building
- Project leadership and team management
- Technical skills (GAAP, IFRS, Excel, SQL, etc.)
- Regulatory compliance experience
- Cross-functional collaboration
- Process improvement and efficiency gains
- Stakeholder communication
- Risk management and controls

FEEDBACK QUALITY STANDARDS:
- Provide specific, actionable recommendations
- Reference industry best practices
- Include examples of improvements
- Prioritize changes by impact
- Consider career level appropriateness
- Address both content and presentation

CRITICAL: Your response must be valid JSON with no additional text, comments, or explanations outside the JSON structure.

Provide analysis in this exact JSON structure:
{
  "rating": <number 0-100>,
  "generalFeedback": "<3-4 paragraphs of comprehensive feedback>",
  "industrySpecificFeedback": "<2-3 paragraphs of industry-specific analysis>",
  "atsImprovements": [
    {
      "category": "<category name>",
      "priority": "<High/Medium/Low>",
      "suggestions": ["<specific suggestion 1>", "<specific suggestion 2>", "<specific suggestion 3>"],
      "impact": "<expected impact description>"
    }
  ],
  "strengths": ["<specific strength 1>", "<specific strength 2>", "<specific strength 3>", "<specific strength 4>"],
  "weaknesses": ["<specific weakness 1>", "<specific weakness 2>", "<specific weakness 3>"],
  "detailedScoring": {
    "contentQuality": <number 0-100>,
    "formatting": <number 0-100>,
    "atsCompatibility": <number 0-100>,
    "industryAlignment": <number 0-100>,
    "achievementQuantification": <number 0-100>,
    "keywordOptimization": <number 0-100>
  },
  "careerLevel": "<Entry/Mid/Senior/Executive>",
  "industryFocus": "<detected industry focus>",
  "recommendedActions": [
    {
      "action": "<specific action to take>",
      "priority": "<Critical/High/Medium/Low>",
      "timeToImplement": "<time estimate>",
      "expectedImpact": "<impact description>"
    }
  ]
}

Categories for atsImprovements: "Content Enhancement", "Formatting & Structure", "Keyword Optimization", "Achievement Quantification", "Industry Alignment", "ATS Compatibility"

IMPORTANT:
- Ensure all strings are properly escaped
- Use only double quotes for JSON strings
- No trailing commas
- No comments or additional text
- Must be valid JSON that can be parsed
- Provide specific, actionable feedback based on professional standards
- Consider the candidate's career level when providing recommendations
- Focus on measurable improvements and industry best practices

${industryPrompt}`,
        },
        {
          role: "user",
          content:
            contentType === "text"
              ? `Analyze this resume content and provide feedback in the exact JSON format specified. Resume content: ${resumeContent.substring(
                  0,
                  3000
                )}` // Limit content to avoid token limits
              : [
                  {
                    type: "text",
                    text: "Analyze this resume image and provide feedback in the exact JSON format specified.",
                  },
                  {
                    type: "image_url",
                    image_url: {
                      url: resumeContent,
                    },
                  },
                ],
        },
      ];

      const requestBody = {
        model: model,
        messages: messages,
        max_tokens: 1500,
        temperature: 0.1, // Lower temperature for more consistent JSON
        response_format: { type: "json_object" },
      };

      const response = await fetch(
        "https://api.groq.com/openai/v1/chat/completions",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${groqApiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        const errorBody = await response.text();
        console.error(
          `Groq API error with ${model}: ${response.status} - ${response.statusText}`,
          errorBody
        );

        // If this is the last model, return the error
        if (model === models[models.length - 1]) {
          if (response.status === 413) {
            return {
              error: "Content too large. Please upload a smaller file.",
            };
          }
          if (response.status === 400) {
            return {
              error: "Invalid request. Please check your file and try again.",
            };
          }
          if (response.status === 401) {
            return {
              error:
                "Invalid API key. Please check your Groq API configuration.",
            };
          }
          if (response.status === 429) {
            return {
              error: "Rate limit exceeded. Please try again in a few minutes.",
            };
          }
          return { error: `AI analysis failed: ${response.statusText}` };
        }

        // Try next model
        continue;
      }

      const data = await response.json();

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        console.error("Invalid response structure:", data);
        continue;
      }

      let analysisContent = data.choices[0].message.content;

      if (typeof analysisContent !== "string") {
        console.error("AI response content is not a string:", analysisContent);
        continue;
      }

      try {
        // Clean the response - remove any non-JSON text
        const cleanedContent = analysisContent.trim();

        // Try to find JSON in the response
        let jsonToParse = cleanedContent;

        // If the response doesn't start with {, try to find JSON
        if (!cleanedContent.startsWith("{")) {
          const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonToParse = jsonMatch[0];
          }
        }

        // Parse the JSON response
        const parsedContent = JSON.parse(jsonToParse);

        // Validate the parsed content against our schema
        const validatedAnalysis = ResumeAnalysisSchema.parse(parsedContent);

        console.log(`Successfully analyzed with model: ${model}`);
        return { success: true, analysis: validatedAnalysis };
      } catch (parseError) {
        console.error(`Failed to parse AI response from ${model}:`, parseError);
        console.error("Raw content:", analysisContent);

        // If this is the last model, try fallback parsing
        if (model === models[models.length - 1]) {
          // Try to create a fallback response
          return createFallbackAnalysis(resumeContent);
        }

        // Try next model
        continue;
      }
    } catch (error) {
      console.error(`Error during AI analysis with ${model}:`, error);

      // If this is the last model, return error
      if (model === models[models.length - 1]) {
        if (error instanceof Error) {
          if (error.message.includes("fetch")) {
            return {
              error:
                "Network error. Please check your connection and try again.",
            };
          }
          if (error.message.includes("timeout")) {
            return { error: "Request timeout. Please try again." };
          }
        }
        return { error: "Failed to analyze resume. Please try again." };
      }

      // Try next model
      continue;
    }
  }

  return { error: "Failed to analyze resume with all available models." };
}

// Fallback analysis when AI fails
function createFallbackAnalysis(resumeContent: string): {
  success: true;
  analysis: AnalysisResult;
} {
  const detectedIndustry = detectIndustry(resumeContent);
  const industryName = industryStandards[detectedIndustry]?.name || "General";
  const specificFeedback = generateSpecificFeedback(
    detectedIndustry,
    resumeContent,
    []
  );
  const hasContact = /email|phone|@/.test(resumeContent);
  const hasExperience = /experience|work|job|position|company/.test(
    resumeContent.toLowerCase()
  );
  const hasSkills = /skill|technolog|languag|framework/.test(
    resumeContent.toLowerCase()
  );
  const hasEducation = /education|degree|university|college|school/.test(
    resumeContent.toLowerCase()
  );

  const rating =
    (hasContact ? 20 : 0) +
    (hasExperience ? 30 : 0) +
    (hasSkills ? 30 : 0) +
    (hasEducation ? 20 : 0);

  const analysis: AnalysisResult = {
    rating: Math.min(rating, 75), // Cap at 75 for fallback
    generalFeedback:
      "Your resume has been processed successfully. The document contains the basic sections expected in a professional resume. To improve its effectiveness, consider adding more specific keywords relevant to your target industry, quantifiable achievements, and ensuring proper formatting for ATS compatibility. Focus on highlighting your unique value proposition and measurable results from your experience.",
    industrySpecificFeedback:
      "Based on the content analysis, your resume would benefit from more industry-specific terminology and achievements. Consider adding relevant certifications, technical skills, and quantified accomplishments that align with your target industry standards.",
    atsImprovements:
      specificFeedback.length > 0
        ? specificFeedback.map((template) => ({
            category: template.category,
            priority: template.priority,
            suggestions: template.suggestions,
            impact: template.impact,
          }))
        : [
            {
              category: "Content Enhancement",
              priority: "High" as const,
              suggestions: [
                "Include more industry-specific keywords relevant to your target role",
                "Add quantifiable achievements with specific metrics and percentages",
                "Use strong action verbs to describe your experience and impact",
              ],
              impact:
                "Significantly improves resume relevance and ATS keyword matching",
            },
            {
              category: "Formatting & Structure",
              priority: "Medium" as const,
              suggestions: [
                "Use consistent formatting throughout the document",
                "Ensure proper section headers and bullet points",
                "Keep the layout clean and ATS-friendly with standard fonts",
              ],
              impact: "Enhances readability and ATS parsing accuracy",
            },
            {
              category: "Achievement Quantification",
              priority: "High" as const,
              suggestions: [
                "Include specific numbers, percentages, and dollar amounts",
                "Add before/after comparisons to show impact",
                "Quantify team sizes, project scope, and business results",
              ],
              impact: "Demonstrates concrete value and professional impact",
            },
          ],
    strengths: [
      "Resume contains essential contact information",
      "Document structure follows professional format",
      "Content includes relevant professional sections",
      "File format is compatible with most ATS systems",
    ],
    weaknesses: [
      "Limited quantification of achievements and results",
      "Could benefit from more industry-specific terminology",
      "May need stronger action verbs and impact statements",
    ],
    detailedScoring: {
      contentQuality: hasExperience ? 65 : 40,
      formatting: 70,
      atsCompatibility: 75,
      industryAlignment: 50,
      achievementQuantification: 40,
      keywordOptimization: hasSkills ? 60 : 35,
    },
    careerLevel: "Mid",
    industryFocus: industryName,
    recommendedActions: [
      {
        action: "Add quantifiable achievements with specific metrics",
        priority: "Critical",
        timeToImplement: "2-3 hours",
        expectedImpact: "Significantly improves resume impact and credibility",
      },
      {
        action: "Include industry-specific keywords and terminology",
        priority: "High",
        timeToImplement: "1-2 hours",
        expectedImpact: "Improves ATS matching and industry relevance",
      },
      {
        action: "Enhance formatting consistency and professional presentation",
        priority: "Medium",
        timeToImplement: "30-60 minutes",
        expectedImpact: "Improves readability and professional appearance",
      },
    ],
  };

  return { success: true, analysis };
}
