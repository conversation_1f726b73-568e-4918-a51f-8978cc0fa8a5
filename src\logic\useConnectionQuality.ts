import { useState, useEffect } from "react";
import { StreamingEvents, ConnectionQuality } from "@heygen/streaming-avatar";
import { useStreamingAvatarSession } from "./useStreamingAvatarSession";

export const useConnectionQuality = () => {
  const { avatarRef } = useStreamingAvatarSession(); // Get avatarRef from the session hook
  const [connectionQuality, setConnectionQuality] = useState<ConnectionQuality>(
    ConnectionQuality.UNKNOWN
  );

  useEffect(() => {
    const avatar = avatarRef.current;
    if (!avatar) return;

    const handleConnectionQualityChange = (event: any) => {
      if (event.detail) {
        setConnectionQuality(event.detail);
      }
    };

    avatar.on(
      StreamingEvents.CONNECTION_QUALITY_CHANGED,
      handleConnectionQualityChange
    );

    return () => {
      avatar.off(
        StreamingEvents.CONNECTION_QUALITY_CHANGED,
        handleConnectionQualityChange
      );
    };
  }, [avatarRef]);

  return { connectionQuality };
};