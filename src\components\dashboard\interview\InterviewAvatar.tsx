"use client";

import StreamingAvatar, {
  AvatarQuality,
  StreamingEvents,
  StartAvatarRequest,
  TaskType,
} from "@heygen/streaming-avatar";
import {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useMemoizedFn } from "ahooks";

import { AvatarVideo } from "./avatar/AvatarSession/AvatarVideo";
import { useStreamingAvatarSession } from "./avatar/logic/useStreamingAvatarSession";

import {
  StreamingAvatarProvider,
  StreamingAvatarSessionState,
} from "./avatar/logic";
import LoadingAnimation from "./avatar/LoadingAnimation";
import { Button } from "@/components/ui/button";

const AVATAR_ID = "Shawn_Therapist_public"; // Male avatar

const DEFAULT_CONFIG: StartAvatarRequest = {
  quality: AvatarQuality.Low,
  avatarName: AVATAR_ID,
};

export interface InterviewAvatarRef {
  stopSession: () => Promise<void>;
}

interface InterviewAvatarProps {
  textToSpeak: string;
  onFinishedSpeaking: () => void;
  onStartedSpeaking?: () => void;
}

const InterviewAvatar = forwardRef<InterviewAvatarRef, InterviewAvatarProps>(
  ({ textToSpeak, onFinishedSpeaking, onStartedSpeaking }, ref) => {
    const {
      initAvatar,
      startAvatar,
      stopAvatar,
      sessionState,
      stream,
      avatarRef,
    } = useStreamingAvatarSession();

    const mediaStream = useRef<HTMLVideoElement>(null);
    const [isSessionReady, setIsSessionReady] = useState(false);
    const [hasUserInteracted, setHasUserInteracted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isSpeaking, setIsSpeaking] = useState(false);
    const [lastSpokenText, setLastSpokenText] = useState<string>("");

    async function fetchAccessToken() {
      const response = await fetch("/api/heygen/create-token", {
        method: "POST",
      });
      return await response.json();
    }

    const startSession = useMemoizedFn(async () => {
      try {
        setIsLoading(true);
        const token = await fetchAccessToken();
        const avatar = initAvatar(token);

        // Set up event listeners
        avatar.on(StreamingEvents.STREAM_READY, () => {
          console.log("Stream is ready");
          setIsSessionReady(true);
          setIsLoading(false);
        });

        avatar.on(StreamingEvents.AVATAR_START_TALKING, () => {
          console.log("Avatar started talking");
          setIsSpeaking(true);
          onStartedSpeaking?.();
        });

        avatar.on(StreamingEvents.AVATAR_STOP_TALKING, () => {
          console.log("Avatar stopped talking");
          setIsSpeaking(false);
          onFinishedSpeaking();
        });

        avatar.on(StreamingEvents.STREAM_DISCONNECTED, () => {
          console.error("Avatar connection lost");
          setIsSessionReady(false);
          setIsSpeaking(false);
          setIsLoading(false);
        });

        // Start the avatar session
        await startAvatar(DEFAULT_CONFIG);
      } catch (error) {
        console.error("Failed to start avatar session:", error);
        setIsSessionReady(false);
        setIsSpeaking(false);
        setIsLoading(false);
        throw error;
      }
    });

    // Handle speaking text - only when session is ready and not currently speaking
    useEffect(() => {
      if (
        textToSpeak &&
        textToSpeak.trim() &&
        avatarRef.current &&
        isSessionReady &&
        !isSpeaking &&
        textToSpeak !== lastSpokenText
      ) {
        console.log("Speaking text:", textToSpeak);
        setLastSpokenText(textToSpeak);

        try {
          avatarRef.current.speak({
            text: textToSpeak.trim(),
            task_type: TaskType.REPEAT,
          });
        } catch (error) {
          console.error("Error speaking:", error);
          setIsSpeaking(false);
          onFinishedSpeaking();
        }
      }
    }, [
      textToSpeak,
      avatarRef,
      isSessionReady,
      isSpeaking,
      lastSpokenText,
      onFinishedSpeaking,
    ]);

    const handleStartSession = useMemoizedFn(async () => {
      if (!hasUserInteracted && !isLoading) {
        setHasUserInteracted(true);
        try {
          await startSession();
        } catch (error) {
          console.error("Failed to start session:", error);
          setHasUserInteracted(false); // Reset on error
        }
      }
    });

    // Handle video stream
    useEffect(() => {
      if (stream && mediaStream.current) {
        mediaStream.current.srcObject = stream;
        mediaStream.current.onloadedmetadata = () => {
          mediaStream.current!.play().catch((err) => {
            console.error("Play failed:", err);
            // Try to play again after a short delay
            setTimeout(() => {
              mediaStream
                .current!.play()
                .catch((err) => console.error("Play failed after retry:", err));
            }, 500);
          });
        };
      }
    }, [stream]);

    useImperativeHandle(ref, () => ({
      async stopSession() {
        if (avatarRef.current) {
          try {
            await stopAvatar();
            console.log("Avatar session stopped gracefully.");
          } catch (error) {
            console.error("Error stopping avatar session:", error);
          }
        }
      },
    }));

    return (
      <div className="relative w-full aspect-video overflow-hidden flex flex-col items-center justify-center rounded-xl bg-zinc-900">
        {!hasUserInteracted ? (
          <Button onClick={handleStartSession} disabled={isLoading}>
            {isLoading ? (
              <>
                <svg
                  className="animate-spin h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Starting...
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <polygon points="5 3 19 12 5 21 5 3" />
                </svg>
              </>
            )}
          </Button>
        ) : sessionState === StreamingAvatarSessionState.INACTIVE ||
          sessionState === StreamingAvatarSessionState.CONNECTING ||
          isLoading ? (
          <LoadingAnimation />
        ) : (
          <AvatarVideo ref={mediaStream} />
        )}

        {/* Debug info - remove in production */}
        {process.env.NODE_ENV === "development" && (
          <div className="absolute bottom-2 left-2 text-xs text-white bg-black bg-opacity-50 p-1 rounded">
            Ready: {isSessionReady ? "Yes" : "No"} | Speaking:{" "}
            {isSpeaking ? "Yes" : "No"}
          </div>
        )}
      </div>
    );
  }
);

InterviewAvatar.displayName = "InterviewAvatar";

const InterviewAvatarWrapper = forwardRef<
  InterviewAvatarRef,
  InterviewAvatarProps
>((props, ref) => {
  return (
    <StreamingAvatarProvider basePath={"https://api.heygen.com"}>
      <InterviewAvatar {...props} ref={ref} />
    </StreamingAvatarProvider>
  );
});

InterviewAvatarWrapper.displayName = "InterviewAvatarWrapper";

export default InterviewAvatarWrapper;
