"use client";

import { useState } from "react";
import { 
  <PERSON><PERSON>hart3, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw, 
  Download, 
  FileText,
  TrendingUp,
  Target,
  Zap,
  Star
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export interface AnalysisResult {
  rating: number;
  generalFeedback: string;
  atsImprovements: Array<{
    category: string;
    suggestions: string[];
  }>;
  strengths: string[];
}

interface ResumeAnalysisProps {
  result: AnalysisResult;
  fileName?: string;
  onReset: () => void;
}

export function ResumeAnalysis({ result, fileName, onReset }: ResumeAnalysisProps) {
  const [activeTab, setActiveTab] = useState<'recommendations' | 'strengths'>('recommendations');

  const getRatingColor = (rating: number) => {
    if (rating >= 80) return "text-green-600 bg-green-100 border-green-200";
    if (rating >= 60) return "text-yellow-600 bg-yellow-100 border-yellow-200";
    return "text-red-600 bg-red-100 border-red-200";
  };

  const getRatingIcon = (rating: number) => {
    if (rating >= 80) return <CheckCircle className="w-6 h-6" />;
    if (rating >= 60) return <AlertTriangle className="w-6 h-6" />;
    return <AlertTriangle className="w-6 h-6" />;
  };

  const getRatingText = (rating: number) => {
    if (rating >= 90) return "Excellent";
    if (rating >= 80) return "Good";
    if (rating >= 70) return "Fair";
    if (rating >= 60) return "Needs Work";
    return "Poor";
  };

  const categoryIcons = {
    "Keywords & Skills": Target,
    "Formatting & Structure": FileText,
    "Content Optimization": TrendingUp,
    "Technical Issues": Zap,
    "Keywords": Target,
    "Formatting": FileText,
    "Content": TrendingUp,
    "Technical": Zap,
    "ATS Compatibility": BarChart3,
    "Structure": FileText
  };

  const getCategoryIcon = (category: string) => {
    const Icon = categoryIcons[category as keyof typeof categoryIcons] || Target;
    return <Icon className="w-5 h-5" />;
  };

  return (
    <div className="p-8 md:p-12">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Resume Analysis</h2>
          {fileName && (
            <p className="text-sm text-gray-500 mt-1">
              {fileName}
            </p>
          )}
        </div>

        {/* Rating Card */}
        <div className="mb-8 p-6 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Overall Score</h3>
              <p className="text-3xl font-bold text-gray-900">
                {result.rating} <span className="text-lg text-gray-500">/ 100</span>
              </p>
              <p className={`text-sm font-medium mt-1 ${
                result.rating >= 80 ? 'text-green-600' : 
                result.rating >= 60 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {getRatingText(result.rating)}
              </p>
            </div>
            <div className="w-24">
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full rounded-full"
                  style={{
                    width: `${result.rating}%`,
                    backgroundColor: result.rating >= 80 ? '#059669' : 
                                    result.rating >= 60 ? '#D97706' : '#DC2626'
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6 border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <Button
              onClick={() => setActiveTab('recommendations')}
              variant={activeTab == "recommendations" ? "default" : "outline"}
              className={`py-4 px-1 border-t-0 border-l-0 border-r-0   border-b-2`}
            >
              Improvement Recommendations
            </Button>
            <Button
              variant={activeTab == "strengths" ? "default" : "outline"}
              onClick={() => setActiveTab('strengths')}
              className={`py-4 px-1 border-t-0 border-l-0 border-r-0   border-b-2`}
            >
              Your Strengths
            </Button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {/* Recommendations Tab */}
          {activeTab === 'recommendations' && (
            <div className="space-y-4">
              {result.atsImprovements.map((improvement, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    {getCategoryIcon(improvement.category)}
                    <span className="ml-2">{improvement.category}</span>
                  </h3>
                  <ul className="space-y-3">
                    {improvement.suggestions.map((suggestion, suggestionIndex) => (
                      <li key={suggestionIndex} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-gray-700 leading-relaxed">{suggestion}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          {/* Strengths Tab */}
          {activeTab === 'strengths' && (
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                <Star className="w-5 h-5 mr-2 text-emerald-600" />
                Your Resume Strengths
              </h3>
              <div className="grid gap-4 md:grid-cols-2">
                {result.strengths.map((strength, index) => (
                  <div key={index} className="flex items-start space-x-3 p-4 bg-emerald-50 rounded-lg border border-emerald-200">
                    <CheckCircle className="w-5 h-5 text-emerald-600 mt-0.5 flex-shrink-0" />
                    <p className="text-emerald-800 font-medium">{strength}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row gap-3">
          <Button
            onClick={onReset}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Analyze Another
          </Button>
          
          <Button
            onClick={() => {
              const reportData = {
                fileName: fileName || 'resume',
                rating: result.rating,
                ratingText: getRatingText(result.rating),
                generalFeedback: result.generalFeedback,
                improvements: result.atsImprovements,
                strengths: result.strengths,
                timestamp: new Date().toLocaleDateString()
              };
              
              const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `resume-analysis-${Date.now()}.json`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50"
          >
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>
    </div>
  );
}