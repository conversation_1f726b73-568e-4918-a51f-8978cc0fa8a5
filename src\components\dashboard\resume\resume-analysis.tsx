"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  Download,
  FileText,
  TrendingUp,
  Target,
  Zap,
  Star,
} from "lucide-react";
import { Button } from "@/components/ui/button";

export interface AnalysisResult {
  rating: number;
  generalFeedback: string;
  industrySpecificFeedback: string;
  atsImprovements: Array<{
    category: string;
    priority: "Critical" | "High" | "Medium" | "Low";
    suggestions: string[];
    impact: string;
  }>;
  strengths: string[];
  weaknesses: string[];
  detailedScoring: {
    contentQuality: number;
    formatting: number;
    atsCompatibility: number;
    industryAlignment: number;
    achievementQuantification: number;
    keywordOptimization: number;
  };
  careerLevel: "Entry" | "Mid" | "Senior" | "Executive";
  industryFocus: string;
  recommendedActions: Array<{
    action: string;
    priority: "Critical" | "High" | "Medium" | "Low";
    timeToImplement: string;
    expectedImpact: string;
  }>;
}

interface ResumeAnalysisProps {
  result: AnalysisResult;
  fileName?: string;
  onReset: () => void;
}

export function ResumeAnalysis({
  result,
  fileName,
  onReset,
}: ResumeAnalysisProps) {
  const [activeTab, setActiveTab] = useState<
    "overview" | "recommendations" | "scoring" | "actions"
  >("overview");

  const getRatingColor = (rating: number) => {
    if (rating >= 80) return "text-green-600 bg-green-100 border-green-200";
    if (rating >= 60) return "text-yellow-600 bg-yellow-100 border-yellow-200";
    return "text-red-600 bg-red-100 border-red-200";
  };

  const getRatingIcon = (rating: number) => {
    if (rating >= 80) return <CheckCircle className="w-6 h-6" />;
    if (rating >= 60) return <AlertTriangle className="w-6 h-6" />;
    return <AlertTriangle className="w-6 h-6" />;
  };

  const getRatingText = (rating: number) => {
    if (rating >= 90) return "Excellent";
    if (rating >= 80) return "Good";
    if (rating >= 70) return "Fair";
    if (rating >= 60) return "Needs Work";
    return "Poor";
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800 border-red-200";
      case "High":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Low":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getCareerLevelIcon = (level: string) => {
    switch (level) {
      case "Entry":
        return "🌱";
      case "Mid":
        return "🚀";
      case "Senior":
        return "⭐";
      case "Executive":
        return "👑";
      default:
        return "📊";
    }
  };

  const downloadReport = () => {
    const reportContent = generateReportText(result, fileName);
    const blob = new Blob([reportContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `resume-analysis-${fileName || "report"}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generateReportText = (analysis: AnalysisResult, filename?: string) => {
    const date = new Date().toLocaleDateString();
    return `
PROFESSIONAL RESUME ANALYSIS REPORT
Generated on: ${date}
File: ${filename || "Resume"}

===========================================
OVERALL ASSESSMENT
===========================================
Overall Score: ${analysis.rating}/100 (${getRatingText(analysis.rating)})
Career Level: ${analysis.careerLevel}
Industry Focus: ${analysis.industryFocus}

===========================================
GENERAL FEEDBACK
===========================================
${analysis.generalFeedback}

===========================================
INDUSTRY-SPECIFIC ANALYSIS
===========================================
${analysis.industrySpecificFeedback}

===========================================
DETAILED SCORING BREAKDOWN
===========================================
Content Quality: ${analysis.detailedScoring.contentQuality}/100
Formatting: ${analysis.detailedScoring.formatting}/100
ATS Compatibility: ${analysis.detailedScoring.atsCompatibility}/100
Industry Alignment: ${analysis.detailedScoring.industryAlignment}/100
Achievement Quantification: ${
      analysis.detailedScoring.achievementQuantification
    }/100
Keyword Optimization: ${analysis.detailedScoring.keywordOptimization}/100

===========================================
KEY STRENGTHS
===========================================
${analysis.strengths
  .map((strength, index) => `${index + 1}. ${strength}`)
  .join("\n")}

===========================================
AREAS FOR IMPROVEMENT
===========================================
${analysis.weaknesses
  .map((weakness, index) => `${index + 1}. ${weakness}`)
  .join("\n")}

===========================================
IMPROVEMENT RECOMMENDATIONS
===========================================
${analysis.atsImprovements
  .map(
    (improvement, index) => `
${index + 1}. ${improvement.category} (${improvement.priority} Priority)
   Impact: ${improvement.impact}
   Suggestions:
   ${improvement.suggestions
     .map((suggestion) => `   • ${suggestion}`)
     .join("\n")}
`
  )
  .join("\n")}

===========================================
PRIORITIZED ACTION PLAN
===========================================
${analysis.recommendedActions
  .map(
    (action, index) => `
${index + 1}. ${action.action} (${action.priority} Priority)
   Time to Implement: ${action.timeToImplement}
   Expected Impact: ${action.expectedImpact}
`
  )
  .join("\n")}

===========================================
NEXT STEPS
===========================================
1. Focus on Critical and High priority items first
2. Implement changes systematically, starting with quick wins
3. Test your updated resume with ATS systems
4. Seek feedback from industry professionals
5. Tailor your resume for specific job applications

This analysis was generated by Preparify's AI-powered resume review system.
For more detailed guidance, consider scheduling a consultation with our career experts.
    `.trim();
  };

  const categoryIcons = {
    "Content Enhancement": TrendingUp,
    "Formatting & Structure": FileText,
    "Keyword Optimization": Target,
    "Achievement Quantification": BarChart3,
    "Industry Alignment": Star,
    "ATS Compatibility": Zap,
  };

  const getCategoryIcon = (category: string) => {
    const Icon =
      categoryIcons[category as keyof typeof categoryIcons] || Target;
    return <Icon className="w-5 h-5" />;
  };

  return (
    <div className="p-8 md:p-12">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Professional Resume Analysis
          </h2>
          {fileName && <p className="text-sm text-gray-500 mt-1">{fileName}</p>}
        </div>

        {/* Enhanced Rating Card */}
        <div className="mb-8 p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">
                Overall Score
              </h3>
              <div className="flex items-center space-x-3">
                <div
                  className={`p-3 rounded-full ${getRatingColor(
                    result.rating
                  )}`}
                >
                  {getRatingIcon(result.rating)}
                </div>
                <div>
                  <p className="text-3xl font-bold text-gray-900">
                    {result.rating}
                    <span className="text-lg text-gray-500 ml-1">/ 100</span>
                  </p>
                  <p
                    className={`text-sm font-medium ${
                      getRatingColor(result.rating).split(" ")[0]
                    }`}
                  >
                    {getRatingText(result.rating)}
                  </p>
                </div>
              </div>
            </div>

            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">
                Career Level
              </h3>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">
                  {getCareerLevelIcon(result.careerLevel)}
                </span>
                <span className="text-lg font-semibold text-gray-900">
                  {result.careerLevel}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">Professional Level</p>
            </div>

            <div className="md:col-span-1">
              <h3 className="text-sm font-medium text-gray-500 mb-2">
                Industry Focus
              </h3>
              <p className="text-lg font-semibold text-gray-900">
                {result.industryFocus}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                Detected Specialization
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: "overview", label: "Overview", icon: BarChart3 },
                {
                  id: "recommendations",
                  label: "Recommendations",
                  icon: TrendingUp,
                },
                { id: "scoring", label: "Detailed Scoring", icon: Target },
                { id: "actions", label: "Action Plan", icon: Zap },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? "border-emerald-500 text-emerald-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>
        {/* Tab Content */}
        <div className="space-y-6">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* General Feedback */}
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  General Assessment
                </h3>
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {result.generalFeedback}
                </p>
              </div>

              {/* Industry-Specific Feedback */}
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <h3 className="text-lg font-bold text-blue-900 mb-4 flex items-center">
                  <Star className="w-5 h-5 mr-2" />
                  Industry-Specific Analysis
                </h3>
                <p className="text-blue-800 leading-relaxed whitespace-pre-line">
                  {result.industrySpecificFeedback}
                </p>
              </div>

              {/* Strengths and Weaknesses */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-green-900 mb-4 flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Key Strengths
                  </h3>
                  <ul className="space-y-2">
                    {result.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-green-800">{strength}</p>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="bg-orange-50 border border-orange-200 rounded-xl p-6">
                  <h3 className="text-lg font-bold text-orange-900 mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2" />
                    Areas for Improvement
                  </h3>
                  <ul className="space-y-2">
                    {result.weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-orange-800">{weakness}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations Tab */}
          {activeTab === "recommendations" && (
            <div className="space-y-4">
              {result.atsImprovements.map((improvement, index) => (
                <div
                  key={index}
                  className="bg-white border border-gray-200 rounded-xl p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-900 flex items-center">
                      {getCategoryIcon(improvement.category)}
                      <span className="ml-2">{improvement.category}</span>
                    </h3>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(
                        improvement.priority
                      )}`}
                    >
                      {improvement.priority} Priority
                    </span>
                  </div>

                  <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800 font-medium">
                      Expected Impact:
                    </p>
                    <p className="text-blue-700">{improvement.impact}</p>
                  </div>

                  <ul className="space-y-3">
                    {improvement.suggestions.map(
                      (suggestion, suggestionIndex) => (
                        <li
                          key={suggestionIndex}
                          className="flex items-start space-x-3"
                        >
                          <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0" />
                          <p className="text-gray-700 leading-relaxed">
                            {suggestion}
                          </p>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              ))}
            </div>
          )}

          {/* Detailed Scoring Tab */}
          {activeTab === "scoring" && (
            <div className="space-y-6">
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-6">
                  Detailed Performance Metrics
                </h3>
                <div className="grid gap-4">
                  {Object.entries(result.detailedScoring).map(
                    ([key, score]) => {
                      const label = key
                        .replace(/([A-Z])/g, " $1")
                        .replace(/^./, (str) => str.toUpperCase());
                      return (
                        <div
                          key={key}
                          className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                        >
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">
                              {label}
                            </h4>
                            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${
                                  score >= 80
                                    ? "bg-green-500"
                                    : score >= 60
                                    ? "bg-yellow-500"
                                    : "bg-red-500"
                                }`}
                                style={{ width: `${score}%` }}
                              ></div>
                            </div>
                          </div>
                          <div className="ml-4 text-right">
                            <span
                              className={`text-2xl font-bold ${getScoreColor(
                                score
                              )}`}
                            >
                              {score}
                            </span>
                            <span className="text-gray-500 text-sm">/100</span>
                          </div>
                        </div>
                      );
                    }
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Action Plan Tab */}
          {activeTab === "actions" && (
            <div className="space-y-4">
              <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-6">
                  Prioritized Action Plan
                </h3>
                <div className="space-y-4">
                  {result.recommendedActions.map((action, index) => (
                    <div
                      key={index}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <h4 className="font-semibold text-gray-900 flex-1">
                          {action.action}
                        </h4>
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(
                            action.priority
                          )}`}
                        >
                          {action.priority}
                        </span>
                      </div>

                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-600">
                            Time to Implement:
                          </span>
                          <p className="text-gray-800">
                            {action.timeToImplement}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-600">
                            Expected Impact:
                          </span>
                          <p className="text-gray-800">
                            {action.expectedImpact}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row gap-3">
          <Button
            onClick={downloadReport}
            className="flex items-center justify-center px-4 py-2 bg-emerald-600 text-white text-sm font-medium rounded-md hover:bg-emerald-700"
          >
            <Download className="w-4 h-4 mr-2" />
            Download Report
          </Button>

          <Button
            onClick={onReset}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Analyze Another
          </Button>

          <Button
            onClick={() => {
              const reportData = {
                fileName: fileName || "resume",
                rating: result.rating,
                ratingText: getRatingText(result.rating),
                generalFeedback: result.generalFeedback,
                improvements: result.atsImprovements,
                strengths: result.strengths,
                timestamp: new Date().toLocaleDateString(),
              };

              const blob = new Blob([JSON.stringify(reportData, null, 2)], {
                type: "application/json",
              });
              const url = URL.createObjectURL(blob);
              const a = document.createElement("a");
              a.href = url;
              a.download = `resume-analysis-${Date.now()}.json`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 hover:bg-gray-50"
          >
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>
    </div>
  );
}
