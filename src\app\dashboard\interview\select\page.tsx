"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { ArrowLeft, Building2, Brain, Users, CheckCircle } from "lucide-react";

const FIRMS = [
  { id: "ey", name: "EY", fullName: "Ernst & Young" },
  { id: "pwc", name: "PwC", fullName: "PricewaterhouseCoopers" },
  { id: "deloitte", name: "Deloitte", fullName: "Deloitte Touche Tohmatsu" },
  { id: "kpmg", name: "KPM<PERSON>", fullName: "KPMG International" },
];

const CATEGORIES = [
  {
    id: "technical",
    name: "Technical Interview",
    description:
      "Focus on technical skills, problem-solving, and industry knowledge",
    icon: <Brain className="w-6 h-6" />,
    color: "bg-blue-50 border-blue-200 text-blue-800",
  },
  {
    id: "behavioral",
    name: "Behavioral Interview",
    description: "Focus on soft skills, leadership, and situational responses",
    icon: <Users className="w-6 h-6" />,
    color: "bg-green-50 border-green-200 text-green-800",
  },
];

export default function InterviewSelectionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedFirm, setSelectedFirm] = useState<string>("");

  useEffect(() => {
    // Check for category parameter in URL
    const categoryParam = searchParams.get("category");
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }
  }, [searchParams]);

  const handleStartInterview = () => {
    if (!selectedCategory || !selectedFirm) return;

    // Navigate to live interview with selected parameters
    router.push(
      `/dashboard/interview/live?category=${selectedCategory}&firm=${selectedFirm}`
    );
  };

  return (
    <div className="min-h-screen bg-background-primary">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/dashboard/interview")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Interview Setup</h1>
            <p className="text-text-secondary">
              Choose your interview type and target firm
            </p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* Category Selection */}
          <div>
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <span className="w-8 h-8 bg-emerald-100 text-emerald-600 rounded-full flex items-center justify-center text-sm font-bold">
                1
              </span>
              Select Interview Type
            </h2>
            <div className="grid md:grid-cols-2 gap-4">
              {CATEGORIES.map((category) => (
                <Card
                  key={category.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedCategory === category.id
                      ? "ring-2 ring-emerald-500 border-emerald-200"
                      : "border-border-primary"
                  }`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${category.color}`}>
                          {category.icon}
                        </div>
                        <CardTitle className="text-lg">
                          {category.name}
                        </CardTitle>
                      </div>
                      {selectedCategory === category.id && (
                        <CheckCircle className="w-5 h-5 text-emerald-600" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{category.description}</CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Firm Selection */}
          <div>
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <span className="w-8 h-8 bg-emerald-100 text-emerald-600 rounded-full flex items-center justify-center text-sm font-bold">
                2
              </span>
              Select Target Firm
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              {FIRMS.map((firm) => (
                <Card
                  key={firm.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedFirm === firm.id
                      ? "ring-2 ring-emerald-500 border-emerald-200"
                      : "border-border-primary"
                  }`}
                  onClick={() => setSelectedFirm(firm.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-gray-50 border border-gray-200">
                          <Building2 className="w-5 h-5 text-gray-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{firm.name}</CardTitle>
                        </div>
                      </div>
                      {selectedFirm === firm.id && (
                        <CheckCircle className="w-5 h-5 text-emerald-600" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {firm.fullName}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Info Section */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="font-semibold text-blue-900 mb-2">
              Interview Information
            </h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>
                • Interview duration: 2 minutes (time-based, not question count)
              </li>
              <li>
                • Questions will be randomly selected from your chosen category
                and firm
              </li>
              <li>
                • EY questions will appear in all interviews as baseline
                questions
              </li>
              <li>
                • You&apos;ll receive a detailed performance report after
                completion
              </li>
            </ul>
          </div>

          {/* Start Button */}
          <div className="flex justify-center pt-4">
            <Button
              onClick={handleStartInterview}
              disabled={!selectedCategory || !selectedFirm}
              size="lg"
              className="px-8"
            >
              Start Interview
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
