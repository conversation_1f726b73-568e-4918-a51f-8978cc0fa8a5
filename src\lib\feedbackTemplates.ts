// Professional feedback templates based on Big 4 auditor standards and industry best practices

export interface FeedbackTemplate {
  category: string;
  priority: "Critical" | "High" | "Medium" | "Low";
  suggestions: string[];
  impact: string;
  examples: string[];
}

export const auditingFeedbackTemplates: FeedbackTemplate[] = [
  {
    category: "Achievement Quantification",
    priority: "Critical",
    suggestions: [
      "Include specific dollar amounts of assets or revenue audited (e.g., 'Audited financial statements for clients with combined revenue of $2.5B')",
      "Quantify team sizes managed and supervised (e.g., 'Led team of 8 staff auditors across 3 concurrent engagements')",
      "Add efficiency metrics and time savings achieved (e.g., 'Reduced audit hours by 15% through process automation')",
      "Include client satisfaction scores or retention rates where available"
    ],
    impact: "Quantified achievements demonstrate concrete value and professional impact, essential for Big 4 progression",
    examples: [
      "Instead of: 'Managed audit engagements' → 'Managed 12 audit engagements totaling $500M in client assets'",
      "Instead of: 'Led audit team' → 'Led team of 6 senior associates on Fortune 500 client audit'",
      "Instead of: 'Improved processes' → 'Streamlined testing procedures, reducing audit hours by 20% and saving $50K annually'"
    ]
  },
  {
    category: "Career Progression Detail",
    priority: "High",
    suggestions: [
      "Include both associate and senior associate experience with clear date ranges for each level",
      "Detail progression from staff-level testing to senior-level planning and review responsibilities",
      "Highlight transition from individual contributor to team leadership roles",
      "Show evolution from technical execution to client relationship management"
    ],
    impact: "Clear career progression demonstrates growth trajectory and readiness for next-level responsibilities",
    examples: [
      "Staff Associate (2019-2021): Performed substantive testing and documentation",
      "Senior Associate (2021-Present): Plan audit procedures, supervise staff, and manage client relationships"
    ]
  },
  {
    category: "Technical Expertise",
    priority: "High",
    suggestions: [
      "Specify experience with complex accounting standards (ASC 606 Revenue Recognition, ASC 842 Leases, IFRS 9 Financial Instruments)",
      "Include experience with specialized audit areas (fair value, impairment assessments, derivatives, stock options)",
      "Detail experience with going concern assessments and subsequent events testing",
      "Highlight experience with SEC reporting and PCAOB inspection readiness"
    ],
    impact: "Technical depth demonstrates senior-level competency and specialization value",
    examples: [
      "Led ASC 606 revenue recognition implementation for technology client with $100M annual revenue",
      "Performed complex fair value testing for Level 3 financial instruments portfolio"
    ]
  },
  {
    category: "Client Management Excellence",
    priority: "High",
    suggestions: [
      "Detail experience managing relationships with C-suite executives and board members",
      "Include examples of managing difficult client conversations and audit findings",
      "Highlight experience working with non-financial client staff (operations, IT, HR)",
      "Show ability to explain complex accounting concepts to non-financial stakeholders"
    ],
    impact: "Client management skills are crucial for senior roles and partnership track progression",
    examples: [
      "Managed relationships with CFO and audit committee for $2B public company client",
      "Successfully communicated material weakness findings to board of directors"
    ]
  },
  {
    category: "Leadership and Development",
    priority: "Medium",
    suggestions: [
      "Include coaching and mentoring responsibilities for junior staff",
      "Detail involvement in recruiting activities and campus interviews",
      "Highlight participation in training program development and delivery",
      "Show involvement in firm initiatives (ESG, diversity & inclusion, social committees)"
    ],
    impact: "Leadership experience demonstrates partnership potential and firm commitment",
    examples: [
      "Mentored 12 new hires through structured onboarding program with 95% retention rate",
      "Led campus recruiting efforts resulting in 15 new hire acceptances"
    ]
  },
  {
    category: "Industry Specialization",
    priority: "Medium",
    suggestions: [
      "Highlight specialized industry experience (financial services, healthcare, technology, manufacturing)",
      "Include experience with industry-specific regulations and compliance requirements",
      "Detail understanding of unique industry risks and accounting considerations",
      "Show progression within industry specialization or cross-industry adaptability"
    ],
    impact: "Industry expertise adds significant value and differentiation in competitive market",
    examples: [
      "Specialized in healthcare audits with deep knowledge of Medicare/Medicaid compliance",
      "Led audits for 8 technology companies with expertise in revenue recognition complexities"
    ]
  }
];

export const consultingFeedbackTemplates: FeedbackTemplate[] = [
  {
    category: "Business Impact Quantification",
    priority: "Critical",
    suggestions: [
      "Include specific cost savings delivered (e.g., 'Identified $5M in annual cost savings through process optimization')",
      "Quantify revenue increases or efficiency improvements achieved",
      "Add client satisfaction metrics and engagement success rates",
      "Include scale metrics (team sizes, project duration, budget managed)"
    ],
    impact: "Quantified business impact demonstrates consulting value and ROI to clients",
    examples: [
      "Led digital transformation initiative resulting in 30% operational efficiency improvement",
      "Delivered $12M cost reduction through supply chain optimization for Fortune 100 client"
    ]
  },
  {
    category: "Strategic Thinking",
    priority: "High",
    suggestions: [
      "Highlight experience developing and implementing strategic initiatives",
      "Include examples of market analysis and competitive intelligence work",
      "Detail experience with business case development and executive presentations",
      "Show ability to translate complex analysis into actionable recommendations"
    ],
    impact: "Strategic thinking capabilities are essential for senior consulting roles and client credibility",
    examples: [
      "Developed market entry strategy for $500M company expanding into Asian markets",
      "Created 5-year strategic plan adopted by C-suite resulting in 25% revenue growth"
    ]
  }
];

export const financeFeedbackTemplates: FeedbackTemplate[] = [
  {
    category: "Deal Experience",
    priority: "Critical",
    suggestions: [
      "Include specific transaction values and deal types (M&A, IPO, debt financing)",
      "Quantify number of deals worked on and success rates",
      "Detail role in deal execution from origination to closing",
      "Include experience with different sectors and deal sizes"
    ],
    impact: "Deal experience demonstrates technical competency and client value in finance roles",
    examples: [
      "Executed 15 M&A transactions totaling $2.5B in enterprise value",
      "Led IPO process for technology company raising $150M in primary offering"
    ]
  },
  {
    category: "Technical Modeling",
    priority: "High",
    suggestions: [
      "Specify types of financial models built (DCF, LBO, merger models, trading comps)",
      "Include complexity indicators (3-statement models, scenario analysis, sensitivity testing)",
      "Detail experience with valuation methodologies and assumptions",
      "Highlight model accuracy and validation experience"
    ],
    impact: "Technical modeling skills are fundamental to finance roles and demonstrate analytical rigor",
    examples: [
      "Built comprehensive LBO model with 5-year projections and multiple exit scenarios",
      "Developed DCF valuation model supporting $500M acquisition recommendation"
    ]
  }
];

export function getIndustryFeedbackTemplates(industry: string): FeedbackTemplate[] {
  switch (industry) {
    case "big4-auditing":
      return auditingFeedbackTemplates;
    case "consulting":
      return consultingFeedbackTemplates;
    case "finance":
      return financeFeedbackTemplates;
    default:
      return [];
  }
}

export function generateSpecificFeedback(
  industry: string,
  resumeContent: string,
  detectedIssues: string[]
): FeedbackTemplate[] {
  const templates = getIndustryFeedbackTemplates(industry);
  const content = resumeContent.toLowerCase();
  
  // Analyze resume content for specific issues
  const hasQuantification = /\d+%|\$\d+|[0-9]+ (million|billion|thousand)/.test(content);
  const hasLeadershipTerms = /(led|managed|supervised|directed|mentored)/.test(content);
  const hasTechnicalSkills = /(excel|sql|python|tableau|sap)/.test(content);
  const hasClientExperience = /(client|customer|stakeholder)/.test(content);
  
  // Filter and prioritize templates based on detected issues
  let relevantTemplates = templates.filter(template => {
    if (template.category === "Achievement Quantification" && !hasQuantification) return true;
    if (template.category === "Leadership and Development" && !hasLeadershipTerms) return true;
    if (template.category === "Technical Expertise" && !hasTechnicalSkills) return true;
    if (template.category === "Client Management Excellence" && !hasClientExperience) return true;
    return false;
  });
  
  // If no specific issues detected, return general improvement templates
  if (relevantTemplates.length === 0) {
    relevantTemplates = templates.slice(0, 3); // Return top 3 templates
  }
  
  return relevantTemplates;
}

export const professionalFeedbackPhrases = {
  positive: [
    "demonstrates strong professional competency",
    "shows clear career progression",
    "effectively communicates value proposition",
    "aligns well with industry standards",
    "exhibits appropriate technical depth"
  ],
  improvement: [
    "would benefit from more specific quantification",
    "could be enhanced with additional technical details",
    "should include more leadership examples",
    "needs stronger achievement metrics",
    "requires more industry-specific terminology"
  ],
  action: [
    "Consider adding specific metrics and percentages",
    "Include concrete examples of business impact",
    "Highlight progression and increased responsibilities",
    "Emphasize client-facing experience and relationship building",
    "Showcase technical competencies and certifications"
  ]
};
