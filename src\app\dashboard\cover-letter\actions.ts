"use server";

import { z } from "zod";

// Simplified schema for cover letter analysis
const CoverLetterAnalysisSchema = z.object({
  rating: z.number().int().min(0).max(100),
  generalFeedback: z.string(),
  improvements: z.array(
    z.object({
      category: z.string(),
      priority: z.enum(["High", "Medium", "Low"]),
      suggestions: z.array(z.string()),
    })
  ),
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  structure: z.object({
    hasOpening: z.boolean(),
    hasBody: z.boolean(),
    hasClosing: z.boolean(),
    hasCallToAction: z.boolean(),
  }),
  tone: z.enum(["Professional", "Casual", "Mixed", "Unclear"]),
  wordCount: z.number(),
});

export type CoverLetterAnalysisResult = z.infer<typeof CoverLetterAnalysisSchema>;

export async function analyzeCoverLetter(
  content: string
): Promise<{ success: boolean; analysis?: CoverLetterAnalysisResult; error?: string }> {
  try {
    console.log("Starting cover letter analysis...");

    // Basic content validation
    if (!content || content.trim().length < 50) {
      return {
        success: false,
        error: "Cover letter content is too short. Please provide at least 50 characters.",
      };
    }

    const models = [
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
    ];

    for (const model of models) {
      try {
        console.log(`Attempting analysis with model: ${model}`);

        const messages = [
          {
            role: "system",
            content: `You are a professional career coach and cover letter expert with 10+ years of experience helping candidates secure positions at top companies.

ANALYSIS FRAMEWORK:
Evaluate cover letters based on:

1. STRUCTURE & FORMAT (25%):
   - Professional opening with clear position reference
   - Well-organized body paragraphs
   - Strong closing with call to action
   - Appropriate length (250-400 words)

2. CONTENT QUALITY (30%):
   - Specific examples and achievements
   - Relevance to target position
   - Company research demonstration
   - Value proposition clarity

3. TONE & STYLE (20%):
   - Professional yet engaging tone
   - Active voice usage
   - Confident but not arrogant
   - Error-free writing

4. PERSONALIZATION (15%):
   - Company-specific details
   - Role-specific requirements addressed
   - Hiring manager name (if available)
   - Industry knowledge demonstration

5. IMPACT & PERSUASION (10%):
   - Compelling narrative
   - Clear differentiation
   - Memorable closing
   - Action-oriented language

CRITICAL: Your response must be valid JSON with no additional text.

Provide analysis in this exact JSON structure:
{
  "rating": <number 0-100>,
  "generalFeedback": "<2-3 paragraphs of feedback>",
  "improvements": [
    {
      "category": "<category name>",
      "priority": "<High/Medium/Low>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>", "<suggestion 3>"]
    }
  ],
  "strengths": ["<strength 1>", "<strength 2>", "<strength 3>"],
  "weaknesses": ["<weakness 1>", "<weakness 2>", "<weakness 3>"],
  "structure": {
    "hasOpening": <boolean>,
    "hasBody": <boolean>,
    "hasClosing": <boolean>,
    "hasCallToAction": <boolean>
  },
  "tone": "<Professional/Casual/Mixed/Unclear>",
  "wordCount": <number>
}

Categories for improvements: "Structure & Format", "Content Quality", "Tone & Style", "Personalization", "Impact & Persuasion"

IMPORTANT: 
- Ensure all strings are properly escaped
- Use only double quotes for JSON strings
- No trailing commas
- Must be valid JSON that can be parsed`,
          },
          {
            role: "user",
            content: `Please analyze this cover letter and provide detailed feedback:\n\n${content}`,
          },
        ];

        const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${process.env.GROQ_API_KEY}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model,
            messages,
            temperature: 0.3,
            max_tokens: 2000,
          }),
        });

        if (!response.ok) {
          console.error(`API error with model ${model}:`, response.status);
          continue;
        }

        const data = await response.json();
        const aiResponse = data.choices?.[0]?.message?.content;

        if (!aiResponse) {
          console.error(`No response from model ${model}`);
          continue;
        }

        console.log(`Raw AI response from ${model}:`, aiResponse);

        // Clean and parse the response
        const cleanedResponse = aiResponse
          .replace(/```json\s*/, "")
          .replace(/```\s*$/, "")
          .replace(/^\s*```\s*/, "")
          .replace(/\s*```\s*$/, "")
          .trim();

        const parsedAnalysis = JSON.parse(cleanedResponse);
        const validatedAnalysis = CoverLetterAnalysisSchema.parse(parsedAnalysis);

        console.log(`Successfully analyzed with model: ${model}`);
        return { success: true, analysis: validatedAnalysis };

      } catch (error) {
        console.error(`Error with model ${model}:`, error);
        continue;
      }
    }

    // Fallback analysis if all models fail
    console.log("All models failed, using fallback analysis");
    return createFallbackCoverLetterAnalysis(content);

  } catch (error) {
    console.error("Cover letter analysis error:", error);
    return {
      success: false,
      error: "Failed to analyze cover letter. Please try again.",
    };
  }
}

// Fallback analysis when AI fails
function createFallbackCoverLetterAnalysis(content: string): {
  success: true;
  analysis: CoverLetterAnalysisResult;
} {
  const wordCount = content.trim().split(/\s+/).length;
  const hasCompanyMention = /company|organization|firm/i.test(content);
  const hasPosition = /position|role|job|opportunity/i.test(content);
  const hasExperience = /experience|worked|managed|led|developed/i.test(content);
  
  let rating = 60; // Base score
  if (wordCount >= 250 && wordCount <= 400) rating += 10;
  if (hasCompanyMention) rating += 5;
  if (hasPosition) rating += 5;
  if (hasExperience) rating += 10;

  const analysis: CoverLetterAnalysisResult = {
    rating: Math.min(rating, 75), // Cap at 75 for fallback
    generalFeedback: "Your cover letter has been processed successfully. The document contains the basic elements expected in a professional cover letter. To improve its effectiveness, consider adding more specific examples of your achievements, demonstrating knowledge of the company, and ensuring a clear connection between your experience and the target role.",
    improvements: [
      {
        category: "Content Quality",
        priority: "High",
        suggestions: [
          "Include specific examples of your achievements with quantifiable results",
          "Demonstrate knowledge of the company and role requirements",
          "Create a stronger connection between your experience and the position",
        ],
      },
      {
        category: "Structure & Format",
        priority: "Medium",
        suggestions: [
          "Ensure clear opening, body, and closing paragraphs",
          "Include a strong call to action in your closing",
          "Maintain appropriate length (250-400 words)",
        ],
      },
      {
        category: "Personalization",
        priority: "High",
        suggestions: [
          "Address the hiring manager by name if possible",
          "Include company-specific details and research",
          "Tailor content to the specific role requirements",
        ],
      },
    ],
    strengths: [
      "Cover letter contains professional language",
      "Document structure follows basic format",
      "Content addresses the application purpose",
    ],
    weaknesses: [
      "Could benefit from more specific examples",
      "May need stronger company research demonstration",
      "Could improve personalization and targeting",
    ],
    structure: {
      hasOpening: content.length > 100,
      hasBody: content.length > 200,
      hasClosing: content.length > 150,
      hasCallToAction: /look forward|contact|interview|discuss/i.test(content),
    },
    tone: "Professional",
    wordCount,
  };

  return { success: true, analysis };
}
