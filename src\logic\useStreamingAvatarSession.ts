import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import StreamingAvatar, {
  AvatarQuality,
  VoiceEmotion,
  TaskType,
  StreamingEvents,
} from "@heygen/streaming-avatar";
import type {
  HeyGenSessionToken,
  AvatarSessionData,
  WelcomeMessage,
  AvatarConfig,
} from "../types/heygen";
import { StreamingAvatarSessionState } from "./index";

export const useStreamingAvatarSession = () => {
  const [sessionState, setSessionState] = useState<StreamingAvatarSessionState>(
    StreamingAvatarSessionState.IDLE
  );
  const [error, setError] = useState<string | null>(null);
  const [isTalking, setIsTalking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState<string>("");

  const avatarRef = useRef<StreamingAvatar | null>(null);
  const sessionDataRef = useRef<AvatarSessionData | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null); // Keep videoRef here as it's tied to the session

  // Default welcome messages
  // Create session token
  const createSessionToken = useCallback(async (): Promise<string> => {
    const response = await fetch("/api/heygen/create-token", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
    });

    if (!response.ok) {
      throw new Error("Failed to create session token");
    }

    const data: HeyGenSessionToken = await response.json();
    return data.data.token;
  }, []);

  // Default welcome messages
  const defaultWelcomeMessages = useMemo<WelcomeMessage[]>(() => [
    { id: "1", text: "Hello! Welcome to our platform.", delay: 1000 },
    {
      id: "2",
      text: "I'm your AI assistant, here to help you today.",
      delay: 3000,
    },
    {
      id: "3",
      text: "Feel free to ask me anything or explore the features available.",
      delay: 5000,
    },
  ], []);

  // Initialize avatar
  const initializeAvatar = useCallback(
    async (config: AvatarConfig = {}, videoElement: HTMLVideoElement) => {
      try {
        setSessionState(StreamingAvatarSessionState.CONNECTING);
        setError(null);
        videoRef.current = videoElement;

        // Use API token directly
        const avatar = new StreamingAvatar({
          token: await createSessionToken(),
        });
        avatarRef.current = avatar;

        // Setup event listeners
        avatar.on(StreamingEvents.AVATAR_START_TALKING, () => {
          console.log("Avatar started talking");
          setIsTalking(true);
        });

        avatar.on(StreamingEvents.AVATAR_STOP_TALKING, () => {
          console.log("Avatar stopped talking");
          setIsTalking(false);
          setCurrentMessage("");
        });

        avatar.on(StreamingEvents.AVATAR_TALKING_MESSAGE, (message: any) => {
          console.log("Avatar talking:", message);
          if (message.detail?.message) {
            setCurrentMessage(message.detail.message);
          }
        });

        avatar.on(StreamingEvents.STREAM_READY, (event: any) => {
          console.log("Stream ready:", event);
          if (videoRef.current && event.detail) {
            videoRef.current.srcObject = event.detail;
            videoRef.current.onloadedmetadata = () => {
              videoRef.current?.play().catch(console.error);
            };
          }
          setSessionState(StreamingAvatarSessionState.CONNECTED);
        });

        avatar.on(StreamingEvents.STREAM_DISCONNECTED, () => {
          console.log("Stream disconnected");
          setSessionState(StreamingAvatarSessionState.DISCONNECTED);
          setIsTalking(false);
          setCurrentMessage("");
        });

        // Create and start avatar session using createStartAvatar
        const sessionData = await avatar.createStartAvatar({
          avatarName: config.avatarName || "default",
        });

        sessionDataRef.current = sessionData;
        console.log("Avatar session started:", sessionData.session_id);
      } catch (err) {
        console.error("Avatar initialization error:", err);
        setError(
          err instanceof Error ? err.message : "Failed to initialize avatar"
        );
        setSessionState(StreamingAvatarSessionState.ERROR);
      }
    },
    [createSessionToken]
  );

  // Send welcome messages
  const sendWelcomeMessages = useCallback(
    async (messages: WelcomeMessage[] = defaultWelcomeMessages) => {
      if (
        !avatarRef.current ||
        sessionState !== StreamingAvatarSessionState.CONNECTED
      ) {
        console.warn("Avatar not ready for welcome messages");
        return;
      }

      try {
        for (const message of messages) {
          // Wait for the specified delay
          await new Promise((resolve) => setTimeout(resolve, message.delay));

          // Send the message
          await avatarRef.current.speak({
            text: message.text,
            task_type: TaskType.REPEAT,
          });

          console.log(`Sent welcome message: ${message.text}`);
        }
      } catch (err) {
        console.error("Error sending welcome messages:", err);
        setError("Failed to send welcome messages");
      }
    },
    [sessionState, defaultWelcomeMessages]
  );

  // Speak custom text
  const speak = useCallback(
    async (text: string) => {
      if (
        !avatarRef.current ||
        sessionState !== StreamingAvatarSessionState.CONNECTED
      ) {
        throw new Error("Avatar not connected");
      }

      try {
        await avatarRef.current.speak({
          text,
          task_type: TaskType.REPEAT,
        });
      } catch (err) {
        console.error("Error speaking:", err);
        throw err;
      }
    },
    [sessionState]
  );

  // Stop avatar
  const stopAvatar = useCallback(async () => {
    if (avatarRef.current) {
      try {
        await avatarRef.current.stopAvatar();
        setSessionState(StreamingAvatarSessionState.DISCONNECTED);
        setIsTalking(false);
        setCurrentMessage("");
        if (videoRef.current) {
          videoRef.current.srcObject = null;
        }
      } catch (err) {
        console.error("Error stopping avatar:", err);
      }
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (avatarRef.current) {
        avatarRef.current.stopAvatar().catch(console.error);
      }
    };
  }, []);

  return {
    // State
    sessionState,
    error,
    isTalking,
    currentMessage,

    // Refs
    avatarRef, // Expose avatarRef for useConnectionQuality
    videoRef,

    // Methods
    initializeAvatar,
    sendWelcomeMessages,
    speak,
    stopAvatar,
  };
};
