"use client";

import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import StreamingAvatar, {
  AvatarQuality,
  VoiceEmotion,
  TaskType,
  StreamingEvents,
} from "@heygen/streaming-avatar";
import type {
  HeyGenSessionToken,
  AvatarSessionData,
  WelcomeMessage,
  AvatarConfig,
} from "../types/heygen";

export const useStreamingAvatar = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTalking, setIsTalking] = useState(false);
  const [currentMessage, setCurrentMessage] = useState<string>("");

  const avatarRef = useRef<StreamingAvatar | null>(null);
  const sessionDataRef = useRef<AvatarSessionData | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  // Default welcome messages
  const defaultWelcomeMessages: WelcomeMessage[] = useMemo(() => [
    { id: "1", text: "Hello! Welcome to our platform.", delay: 1000 },
    {
      id: "2",
      text: "I'm your AI assistant, here to help you today.",
      delay: 3000,
    },
    {
      id: "3",
      text: "Feel free to ask me anything or explore the features available.",
      delay: 5000,
    },
  ], []);

  // Create session token
  const createSessionToken = useCallback(async (): Promise<string> => {
    const response = await fetch("/api/heygen/create-token", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
    });

    if (!response.ok) {
      throw new Error("Failed to create session token");
    }

    const data: HeyGenSessionToken = await response.json();
    return data.data.token;
  }, []);

  // Initialize avatar
  const initializeAvatar = useCallback(
    async (config: AvatarConfig = {}) => {
      try {
        setIsLoading(true);
        setError(null);

        // Use your HeyGen API token directly, not a session token
        const avatar = new StreamingAvatar({
          token: await createSessionToken(), // Your actual API token
        });
        avatarRef.current = avatar;

        // Setup event listeners first
        avatar.on(StreamingEvents.AVATAR_START_TALKING, () => {
          console.log("Avatar started talking");
          setIsTalking(true);
        });

        avatar.on(StreamingEvents.AVATAR_STOP_TALKING, () => {
          console.log("Avatar stopped talking");
          setIsTalking(false);
          setCurrentMessage("");
        });

        avatar.on(StreamingEvents.STREAM_READY, (event: any) => {
          console.log("Stream ready:", event);
          if (videoRef.current && event.detail) {
            videoRef.current.srcObject = event.detail;
            videoRef.current.onloadedmetadata = () => {
              videoRef.current?.play().catch(console.error);
            };
          }
          setIsConnected(true);
        });

        // Use createStartAvatar instead of newSession + startSession
        const sessionData = await avatar.createStartAvatar({
          avatarName: config.avatarName || "default",
          // quality:
          //   config.quality === "high"
          //     ? "high"
          //     : config.quality === "medium"
          //     ? "medium"
          //     : "low",
          // voice: {
          //   voiceId: config.voiceId || "default",
          //   rate: config.rate || 1.0,
          // },
        });

        sessionDataRef.current = sessionData;
        console.log("Avatar session started:", sessionData.session_id);
      } catch (err) {
        console.error("Avatar initialization error:", err);
        setError(
          err instanceof Error ? err.message : "Failed to initialize avatar"
        );
      } finally {
        setIsLoading(false);
      }
    },
    [createSessionToken] // Add createSessionToken to dependencies
  );

  // Send welcome messages
  const sendWelcomeMessages = useCallback(
    async (messages: WelcomeMessage[] = defaultWelcomeMessages) => {
      if (!avatarRef.current || !isConnected) {
        console.warn("Avatar not ready for welcome messages");
        return;
      }

      try {
        for (const message of messages) {
          // Wait for the specified delay
          await new Promise((resolve) => setTimeout(resolve, message.delay));

          // Send the message
          await avatarRef.current.speak({
            text: message.text,
            task_type: TaskType.REPEAT,
          });

          console.log(`Sent welcome message: ${message.text}`);
        }
      } catch (err) {
        console.error("Error sending welcome messages:", err);
        setError("Failed to send welcome messages");
      }
    },
    [isConnected, defaultWelcomeMessages]
  );

  // Speak custom text
  const speak = useCallback(
    async (text: string) => {
      if (!avatarRef.current || !isConnected) {
        throw new Error("Avatar not connected");
      }

      try {
        await avatarRef.current.speak({
          text,
          task_type: TaskType.REPEAT,
        });
      } catch (err) {
        console.error("Error speaking:", err);
        throw err;
      }
    },
    [isConnected]
  );

  // Stop avatar
  const stopAvatar = useCallback(async () => {
    if (avatarRef.current) {
      try {
        await avatarRef.current.stopAvatar();
        setIsConnected(false);
        setIsTalking(false);
        setCurrentMessage("");
      } catch (err) {
        console.error("Error stopping avatar:", err);
      }
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (avatarRef.current) {
        avatarRef.current.stopAvatar().catch(console.error);
      }
    };
  }, []);

  return {
    // State
    isConnected,
    isLoading,
    error,
    isTalking,
    currentMessage,

    // Refs
    videoRef,

    // Methods
    initializeAvatar,
    sendWelcomeMessages,
    speak,
    stopAvatar,
  };
};
