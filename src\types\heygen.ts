export interface HeyGenSessionToken {
  data: {
    token: string;
  };
}

export interface AvatarSessionData {
  session_id: string;
  access_token: string;
  url: string;
  is_paid: boolean;
  session_duration_limit: number;
}

export interface WelcomeMessage {
  id: string;
  text: string;
  delay: number; // delay in milliseconds before sending
}

export interface AvatarConfig {
  avatarName?: string;
  quality?: "high" | "medium" | "low";
  voiceId?: string;
  emotion?: "EXCITED" | "FRIENDLY" | "SERIOUS" | "SOOTHING" | "BROADCASTER";
  rate?: number; // 0.5 to 1.5
}
