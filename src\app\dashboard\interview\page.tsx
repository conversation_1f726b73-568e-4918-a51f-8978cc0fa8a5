"use client";

import { useRouter } from "next/navigation";
import { Mi<PERSON>, Award, History } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function InterviewIntroPage() {
  const router = useRouter();

  const features = [
    {
      icon: <Mic className="w-6 h-6 text-emerald-600" />,
      title: "Voice-Activated",
      description: "Speak naturally - no typing required",
    },
    {
      icon: <Award className="w-6 h-6 text-emerald-600" />,
      title: "Comprehensive Report",
      description: "Detailed performance insights",
    },
  ];

  return (
    <main className="min-h-screen bg-gradient-to-b from-emerald-50 to-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 py-12">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Mock Interview
          </h1>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Practice your interview skills with our AI-powered mock interview.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 max-w-2xl mx-auto">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-gray-50 p-6 rounded-xl hover:bg-emerald-50 transition-colors"
              >
                <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                  {feature.icon}
                </div>
                <h3 className="font-semibold text-center mb-1">
                  {feature.title}
                </h3>
                <p className="text-sm text-gray-600 text-center">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center space-y-4">
            <Button
              onClick={() => router.push("/dashboard/interview/select")}
              size={"lg"}
            >
              <span>Start Interview</span>
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 5l7 7m0 0l-7 7m7-7H3"
                />
              </svg>
            </Button>

            <div>
              <Button
                variant="outline"
                onClick={() => router.push("/dashboard/interview/history")}
                className="flex items-center gap-2"
              >
                <History className="w-4 h-4" />
                View History
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
