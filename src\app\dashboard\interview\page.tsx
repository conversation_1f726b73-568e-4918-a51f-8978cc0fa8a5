"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Mic,
  Award,
  History,
  Play,
  TrendingUp,
  Clock,
  Target,
  Calendar,
  FileText,
  BarChart3,
  Loader2,
  ChevronRight,
  Star,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface InterviewHistory {
  id: string;
  date: string;
  firm: string;
  category: string;
  score: number;
  duration: string;
  questionsAnswered: number;
}

export default function InterviewDashboard() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [recentInterviews, setRecentInterviews] = useState<InterviewHistory[]>(
    []
  );
  const [stats, setStats] = useState({
    totalInterviews: 0,
    averageScore: 0,
    totalTime: 0,
    improvement: 0,
  });

  useEffect(() => {
    // Simulate loading and fetch data
    const loadData = async () => {
      setIsLoading(true);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Mock data - in real app, this would come from your database
      const mockInterviews: InterviewHistory[] = [
        {
          id: "1",
          date: "2024-01-15",
          firm: "Deloitte",
          category: "Behavioral",
          score: 85,
          duration: "25 min",
          questionsAnswered: 8,
        },
        {
          id: "2",
          date: "2024-01-12",
          firm: "PwC",
          category: "Technical",
          score: 78,
          duration: "30 min",
          questionsAnswered: 10,
        },
        {
          id: "3",
          date: "2024-01-10",
          firm: "KPMG",
          category: "Communication",
          score: 82,
          duration: "22 min",
          questionsAnswered: 7,
        },
      ];

      setRecentInterviews(mockInterviews);
      setStats({
        totalInterviews: mockInterviews.length,
        averageScore: Math.round(
          mockInterviews.reduce((sum, interview) => sum + interview.score, 0) /
            mockInterviews.length
        ),
        totalTime: mockInterviews.reduce(
          (sum, interview) => sum + parseInt(interview.duration),
          0
        ),
        improvement: 12, // Mock improvement percentage
      });

      setIsLoading(false);
    };

    loadData();
  }, []);

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return "bg-green-100 text-green-800";
    if (score >= 70) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-emerald-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Loading Dashboard
          </h2>
          <p className="text-gray-600">Preparing your interview analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Interview Dashboard
          </h1>
          <p className="text-gray-600">
            Track your progress and improve your interview skills with
            AI-powered practice sessions.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Interviews
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.totalInterviews}
                  </p>
                </div>
                <div className="p-3 bg-emerald-100 rounded-full">
                  <Target className="w-6 h-6 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Average Score
                  </p>
                  <p
                    className={`text-2xl font-bold ${getScoreColor(
                      stats.averageScore
                    )}`}
                  >
                    {stats.averageScore}%
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <BarChart3 className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Practice Time
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.totalTime}m
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Improvement
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    +{stats.improvement}%
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Play className="w-5 h-5 text-emerald-600" />
                  <span>Quick Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={() => router.push("/dashboard/interview/select")}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  size="lg"
                >
                  <Mic className="w-5 h-5 mr-2" />
                  Start New Interview
                </Button>

                <Button
                  onClick={() => router.push("/dashboard/interview/history")}
                  variant="outline"
                  className="w-full"
                >
                  <History className="w-4 h-4 mr-2" />
                  View All History
                </Button>

                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Practice Categories
                  </h4>
                  <div className="space-y-2">
                    {["Behavioral", "Technical", "Communication"].map(
                      (category) => (
                        <button
                          key={category}
                          onClick={() =>
                            router.push(
                              `/dashboard/interview/select?category=${category.toLowerCase()}`
                            )
                          }
                          className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                        >
                          <span className="text-sm font-medium text-gray-700">
                            {category}
                          </span>
                        </button>
                      )
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Interviews */}
          <div className="lg:col-span-2">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <History className="w-5 h-5 text-blue-600" />
                    <span>Recent Interviews</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push("/dashboard/interview/history")}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    View All
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentInterviews.length === 0 ? (
                  <div className="text-center py-8">
                    <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No interviews yet
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Start your first mock interview to see your progress here.
                    </p>
                    <Button
                      onClick={() => router.push("/dashboard/interview/select")}
                      className="bg-emerald-600 hover:bg-emerald-700"
                    >
                      Start First Interview
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {recentInterviews.map((interview) => (
                      <div
                        key={interview.id}
                        className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                        onClick={() =>
                          router.push(
                            `/dashboard/interview/report/${interview.id}`
                          )
                        }
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-medium text-gray-900">
                                {interview.firm}
                              </h4>
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {interview.category}
                              </span>
                              <span
                                className={`px-2 py-1 text-xs rounded-full ${getScoreBadgeColor(
                                  interview.score
                                )}`}
                              >
                                {interview.score}%
                              </span>
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-4 h-4" />
                                <span>
                                  {new Date(
                                    interview.date
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4" />
                                <span>{interview.duration}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <FileText className="w-4 h-4" />
                                <span>
                                  {interview.questionsAnswered} questions
                                </span>
                              </div>
                            </div>
                          </div>
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Why Practice with Preparify?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: <Mic className="w-8 h-8 text-emerald-600" />,
                title: "Voice-Activated Practice",
                description:
                  "Natural conversation flow with AI interviewer - no typing required",
              },
              {
                icon: <Star className="w-8 h-8 text-emerald-600" />,
                title: "STAR Technique Focus",
                description:
                  "Get feedback specifically on Situation, Task, Action, Result structure",
              },
              {
                icon: <Award className="w-8 h-8 text-emerald-600" />,
                title: "Detailed Analytics",
                description:
                  "Comprehensive reports with actionable insights for improvement",
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className="bg-white shadow-sm hover:shadow-md transition-shadow"
              >
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
