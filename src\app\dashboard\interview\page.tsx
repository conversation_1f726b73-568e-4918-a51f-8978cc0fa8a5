"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Mic,
  Award,
  History,
  Play,
  Calendar,
  FileText,
  Loader2,
  ChevronRight,
  Star,
  Clock,
  Building2,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

interface InterviewSession {
  id: string;
  category: string;
  firm: string;
  status: string;
  startedAt: string;
  completedAt?: string;
  durationMinutes: number;
  hasReport: boolean;
}

export default function InterviewDashboard() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/interview/history");
        if (!response.ok) {
          throw new Error("Failed to fetch interview history");
        }
        const data = await response.json();
        setSessions(data.sessions || []);
      } catch (err) {
        console.error("Error fetching history:", err);
        setError("Failed to load interview history");
      } finally {
        setIsLoading(false);
      }
    };

    fetchHistory();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "in_progress":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>
        );
      case "abandoned":
        return <Badge className="bg-red-100 text-red-800">Abandoned</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-emerald-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Loading Dashboard
          </h2>
          <p className="text-gray-600">Preparing your interview analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Interview Practice
          </h1>
          <p className="text-gray-600">
            Practice your interview skills with AI-powered mock interviews and
            track your progress.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Play className="w-5 h-5 text-emerald-600" />
                  <span>Quick Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={() => router.push("/dashboard/interview/select")}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  size="lg"
                >
                  <Mic className="w-5 h-5 mr-2" />
                  Start New Interview
                </Button>

                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Practice Categories
                  </h4>
                  <div className="space-y-2">
                    {["Behavioral", "Technical", "Communication"].map(
                      (category) => (
                        <button
                          key={category}
                          onClick={() =>
                            router.push(
                              `/dashboard/interview/select?category=${category.toLowerCase()}`
                            )
                          }
                          className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                        >
                          <span className="text-sm font-medium text-gray-700">
                            {category}
                          </span>
                        </button>
                      )
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Interview History */}
          <div className="lg:col-span-2">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <History className="w-5 h-5 text-blue-600" />
                  <span>Interview History</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {error && (
                  <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
                    <AlertCircle className="w-5 h-5 text-red-600" />
                    <span className="text-red-700">{error}</span>
                  </div>
                )}

                {sessions.length === 0 && !error ? (
                  <div className="text-center py-8">
                    <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No interviews yet
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Start your first mock interview to see your progress here.
                    </p>
                    <Button
                      onClick={() => router.push("/dashboard/interview/select")}
                      className="bg-emerald-600 hover:bg-emerald-700"
                    >
                      Start First Interview
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {sessions.map((session) => (
                      <div
                        key={session.id}
                        className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                        onClick={() => {
                          if (session.hasReport) {
                            router.push(
                              `/dashboard/interview/report/${session.id}`
                            );
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h4 className="font-medium text-gray-900">
                                {session.firm}
                              </h4>
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {session.category}
                              </span>
                              {getStatusBadge(session.status)}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-4 h-4" />
                                <span>
                                  {format(
                                    new Date(session.startedAt),
                                    "MMM d, yyyy"
                                  )}
                                </span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4" />
                                <span>
                                  {formatDuration(session.durationMinutes)}
                                </span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Building2 className="w-4 h-4" />
                                <span>{session.firm}</span>
                              </div>
                            </div>
                          </div>
                          {session.hasReport && (
                            <ChevronRight className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Why Practice with Preparify?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: <Mic className="w-8 h-8 text-emerald-600" />,
                title: "Voice-Activated Practice",
                description:
                  "Natural conversation flow with AI interviewer - no typing required",
              },
              {
                icon: <Star className="w-8 h-8 text-emerald-600" />,
                title: "STAR Technique Focus",
                description:
                  "Get feedback specifically on Situation, Task, Action, Result structure",
              },
              {
                icon: <Award className="w-8 h-8 text-emerald-600" />,
                title: "Detailed Analytics",
                description:
                  "Comprehensive reports with actionable insights for improvement",
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className="bg-white shadow-sm hover:shadow-md transition-shadow"
              >
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
