// Test suite for enhanced resume analysis system

import { detectIndustry, getIndustrySpecificPrompt } from "./industryAnalysis";
import { generateSpecificFeedback, getIndustryFeedbackTemplates } from "./feedbackTemplates";

// Sample resume content for testing
export const sampleResumes = {
  bigFourAuditor: `
    <PERSON>, CPA
    Senior Associate, Deloitte & Touche LLP
    
    EXPERIENCE:
    Senior Associate (2021-Present)
    • Led audit engagements for clients with combined revenue of $2.5B
    • Managed team of 6 staff auditors across 3 concurrent public company audits
    • Performed complex ASC 606 revenue recognition testing for technology clients
    • Conducted SOX 404 compliance testing and internal controls evaluation
    • Reduced audit hours by 15% through process automation and efficiency improvements
    
    Staff Associate (2019-2021)
    • Performed substantive testing for cash, accounts receivable, and inventory
    • Executed walkthrough procedures and documented business processes
    • Assisted in preparation of audit workpapers and supporting documentation
    
    EDUCATION:
    Bachelor of Science in Accounting, State University
    CPA License - State Board of Accountancy
    
    SKILLS:
    GAAP, IFRS, SOX Compliance, Excel (Advanced), SQL, TeamMate, Risk Assessment
  `,
  
  consultant: `
    <PERSON>
    Senior Consultant, McK<PERSON>ey & Company
    
    EXPERIENCE:
    Senior Consultant (2020-Present)
    • Led digital transformation initiative for Fortune 500 client, resulting in 30% operational efficiency improvement
    • Developed market entry strategy for $500M company expanding into Asian markets
    • Managed cross-functional team of 12 consultants across 4 workstreams
    • Delivered $12M cost reduction through supply chain optimization
    
    Business Analyst (2018-2020)
    • Conducted market research and competitive analysis for strategy development
    • Built financial models and business cases for executive decision-making
    • Supported client presentations and stakeholder management
    
    EDUCATION:
    MBA, Harvard Business School
    Bachelor of Arts in Economics, Yale University
    
    SKILLS:
    Strategic Planning, Process Improvement, Excel, PowerPoint, SQL, Python
  `,
  
  generic: `
    Bob Johnson
    Marketing Manager
    
    EXPERIENCE:
    Marketing Manager (2020-Present)
    • Managed marketing campaigns
    • Worked with team members
    • Improved processes
    • Handled customer relationships
    
    Marketing Associate (2018-2020)
    • Assisted with marketing activities
    • Supported team projects
    • Learned new skills
    
    EDUCATION:
    Bachelor of Arts in Marketing
    
    SKILLS:
    Marketing, Communication, Teamwork
  `
};

// Test functions
export function testIndustryDetection() {
  console.log("Testing Industry Detection...");
  
  const auditResult = detectIndustry(sampleResumes.bigFourAuditor);
  const consultingResult = detectIndustry(sampleResumes.consultant);
  const genericResult = detectIndustry(sampleResumes.generic);
  
  console.log(`Big Four Auditor Resume -> Detected: ${auditResult}`);
  console.log(`Consultant Resume -> Detected: ${consultingResult}`);
  console.log(`Generic Resume -> Detected: ${genericResult}`);
  
  return {
    audit: auditResult === "big4-auditing",
    consulting: consultingResult === "consulting",
    generic: genericResult === "general"
  };
}

export function testFeedbackGeneration() {
  console.log("Testing Feedback Generation...");
  
  const auditFeedback = generateSpecificFeedback("big4-auditing", sampleResumes.bigFourAuditor, []);
  const consultingFeedback = generateSpecificFeedback("consulting", sampleResumes.consultant, []);
  const genericFeedback = generateSpecificFeedback("general", sampleResumes.generic, []);
  
  console.log(`Audit Feedback Categories: ${auditFeedback.map(f => f.category).join(", ")}`);
  console.log(`Consulting Feedback Categories: ${consultingFeedback.map(f => f.category).join(", ")}`);
  console.log(`Generic Feedback Categories: ${genericFeedback.map(f => f.category).join(", ")}`);
  
  return {
    auditFeedbackCount: auditFeedback.length,
    consultingFeedbackCount: consultingFeedback.length,
    genericFeedbackCount: genericFeedback.length
  };
}

export function testPromptGeneration() {
  console.log("Testing Industry-Specific Prompt Generation...");
  
  const auditPrompt = getIndustrySpecificPrompt("big4-auditing");
  const consultingPrompt = getIndustrySpecificPrompt("consulting");
  const financePrompt = getIndustrySpecificPrompt("finance");
  
  return {
    auditPromptLength: auditPrompt.length,
    consultingPromptLength: consultingPrompt.length,
    financePromptLength: financePrompt.length,
    hasAuditKeywords: auditPrompt.includes("GAAP") && auditPrompt.includes("SOX"),
    hasConsultingKeywords: consultingPrompt.includes("Strategic Planning") && consultingPrompt.includes("Process Improvement"),
    hasFinanceKeywords: financePrompt.includes("Financial Modeling") && financePrompt.includes("Valuation")
  };
}

export function runAllTests() {
  console.log("=== Enhanced Resume Analysis System Tests ===\n");
  
  const industryTests = testIndustryDetection();
  const feedbackTests = testFeedbackGeneration();
  const promptTests = testPromptGeneration();
  
  console.log("\n=== Test Results Summary ===");
  console.log(`Industry Detection: ${Object.values(industryTests).every(Boolean) ? "PASS" : "FAIL"}`);
  console.log(`Feedback Generation: ${feedbackTests.auditFeedbackCount > 0 && feedbackTests.consultingFeedbackCount > 0 ? "PASS" : "FAIL"}`);
  console.log(`Prompt Generation: ${promptTests.hasAuditKeywords && promptTests.hasConsultingKeywords && promptTests.hasFinanceKeywords ? "PASS" : "FAIL"}`);
  
  return {
    industryDetection: industryTests,
    feedbackGeneration: feedbackTests,
    promptGeneration: promptTests,
    overallSuccess: Object.values(industryTests).every(Boolean) && 
                   feedbackTests.auditFeedbackCount > 0 && 
                   promptTests.hasAuditKeywords
  };
}

// Quality validation functions
export function validateResumeAnalysisQuality(analysisResult: any) {
  const qualityChecks = {
    hasDetailedScoring: analysisResult.detailedScoring && Object.keys(analysisResult.detailedScoring).length === 6,
    hasIndustryFeedback: analysisResult.industrySpecificFeedback && analysisResult.industrySpecificFeedback.length > 50,
    hasPrioritizedActions: analysisResult.recommendedActions && analysisResult.recommendedActions.length > 0,
    hasWeaknesses: analysisResult.weaknesses && analysisResult.weaknesses.length > 0,
    hasImpactStatements: analysisResult.atsImprovements && analysisResult.atsImprovements.every((imp: any) => imp.impact),
    hasCareerLevel: analysisResult.careerLevel && ["Entry", "Mid", "Senior", "Executive"].includes(analysisResult.careerLevel),
    hasIndustryFocus: analysisResult.industryFocus && analysisResult.industryFocus.length > 0
  };
  
  const passedChecks = Object.values(qualityChecks).filter(Boolean).length;
  const totalChecks = Object.keys(qualityChecks).length;
  
  return {
    qualityScore: (passedChecks / totalChecks) * 100,
    passedChecks,
    totalChecks,
    details: qualityChecks
  };
}

// Big 4 specific validation
export function validateBigFourStandards(analysisResult: any, resumeContent: string) {
  const bigFourChecks = {
    detectsQuantification: analysisResult.atsImprovements.some((imp: any) => 
      imp.category.includes("Quantification") || imp.suggestions.some((s: string) => s.includes("metric"))
    ),
    identifiesCareerProgression: analysisResult.atsImprovements.some((imp: any) => 
      imp.category.includes("Career") || imp.suggestions.some((s: string) => s.includes("progression"))
    ),
    emphasizesTechnicalSkills: analysisResult.atsImprovements.some((imp: any) => 
      imp.category.includes("Technical") || imp.suggestions.some((s: string) => s.includes("technical"))
    ),
    highlightsClientManagement: analysisResult.atsImprovements.some((imp: any) => 
      imp.suggestions.some((s: string) => s.toLowerCase().includes("client"))
    ),
    providesSpecificExamples: analysisResult.atsImprovements.some((imp: any) => 
      imp.suggestions.some((s: string) => s.includes("$") || s.includes("%"))
    ),
    hasIndustryTerminology: analysisResult.industrySpecificFeedback.toLowerCase().includes("audit") || 
                           analysisResult.industrySpecificFeedback.toLowerCase().includes("gaap"),
    appropriateRating: analysisResult.rating >= 0 && analysisResult.rating <= 100
  };
  
  const passedChecks = Object.values(bigFourChecks).filter(Boolean).length;
  const totalChecks = Object.keys(bigFourChecks).length;
  
  return {
    bigFourScore: (passedChecks / totalChecks) * 100,
    passedChecks,
    totalChecks,
    details: bigFourChecks
  };
}

// Export test runner for use in development
export default {
  runAllTests,
  validateResumeAnalysisQuality,
  validateBigFourStandards,
  sampleResumes
};
